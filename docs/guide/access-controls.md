# 8. ACCESS CONTROLS

## 8.1 Access Controls Overview

Access controls allow administrative users and supervisors to Add, Remove, and Edit user profiles, permissions, and roles.

### 8.1 Add, Remove and Edit Users

1. Click on **Access Controls** then **User Accounts** in the main menu. The system will display a table with all the users in the system.

2. To add a new user, click on the blue **Add User** button then enter the following information:
   - Username
   - First name
   - Middle name
   - Last name
   - Password
   - Confirm password
   - Sex
   - Roles
   - Lab section
   - Date of birth

   Note: Roles and lab sections will only be available if configured in the system.

3. To edit or remove a user, click on the **Edit** or **Disable** buttons.

### 8.2 Permissions

The Permissions page enables the administrator to define what each user role can do or not do within the system.

To set a permission:
- Check the box in the appropriate row under the column for the role you wish to define a permission for.
- When a box is checked, every user with that role assigned will be able to carry out that activity.

### 8.3 Roles

To access roles:
1. Click on **Access Controls**, then **Roles** in the main menu. The system will display all the currently available roles in the system.
2. To create a new role, click on the green **Create Role** button. You can then give a name to the role as well as assign permissions.
3. To edit or remove a role, click on the **Edit** or **Delete** buttons.
