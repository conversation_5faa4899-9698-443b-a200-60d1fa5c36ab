{"version": 3, "sources": ["../../../../node_modules/@vueuse/shared/index.mjs", "../../../../node_modules/@vueuse/core/index.mjs", "../../../../node_modules/tabbable/src/index.js", "../../../../node_modules/focus-trap/index.js", "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, ref, watch, customRef, getCurrentScope, onScopeDispose, effectScope, getCurrentInstance, provide, inject, isVue3, version, isRef, unref, computed, reactive, toRefs as toRefs$1, toRef as toRef$1, isVue2, set as set$1, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue-demi';\n\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, {\n    ...options,\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  });\n  return readonly(result);\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = ref(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = typeof fn === \"function\" ? fn : fn.get;\n  const set = typeof fn === \"function\" ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get();\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = /* @__PURE__ */ new Set();\n  const off = (fn) => {\n    fns.delete(fn);\n  };\n  const on = (fn) => {\n    fns.add(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (...args) => {\n    return Promise.all(Array.from(fns).map((fn) => fn(...args)));\n  };\n  return {\n    on,\n    off,\n    trigger\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return (...args) => {\n    if (!initialized) {\n      state = scope.run(() => stateFactory(...args));\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nconst localProvidedStateMap = /* @__PURE__ */ new WeakMap();\n\nconst provideLocal = (key, value) => {\n  var _a;\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"provideLocal must be called in setup\");\n  if (!localProvidedStateMap.has(instance))\n    localProvidedStateMap.set(instance, /* @__PURE__ */ Object.create(null));\n  const localProvidedState = localProvidedStateMap.get(instance);\n  localProvidedState[key] = value;\n  provide(key, value);\n};\n\nconst injectLocal = (...args) => {\n  var _a;\n  const key = args[0];\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"injectLocal must be called in setup\");\n  if (localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))\n    return localProvidedStateMap.get(instance)[key];\n  return inject(...args);\n};\n\nfunction createInjectionState(composable, options) {\n  const key = (options == null ? void 0 : options.injectionKey) || Symbol(\"InjectionState\");\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provideLocal(key, state);\n    return state;\n  };\n  const useInjectedState = () => injectLocal(key);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!state) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  if (!isVue3 && !version.startsWith(\"2.7.\")) {\n    if (process.env.NODE_ENV !== \"production\")\n      throw new Error(\"[VueUse] extendRef only works in Vue 2.7 or above.\");\n    return;\n  }\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = { ...obj };\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction toValue(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\nconst resolveUnref = toValue;\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : toValue;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(\n    keys.map((key) => {\n      const value = obj[key];\n      return [\n        key,\n        typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n      ];\n    })\n  );\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => !predicate(toValue(v), k))) : Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nconst isClient = typeof window !== \"undefined\" && typeof document !== \"undefined\";\nconst isWorker = typeof WorkerGlobalScope !== \"undefined\" && globalThis instanceof WorkerGlobalScope;\nconst isDef = (val) => typeof val !== \"undefined\";\nconst notNullish = (val) => val != null;\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nconst isIOS = /* @__PURE__ */ getIsIOS();\nfunction getIsIOS() {\n  var _a, _b;\n  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  const filter = (invoke) => {\n    const duration = toValue(ms);\n    const maxDuration = toValue(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(invoke());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(ms, trailing = true, leading = true, rejectOnCancel = false) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = toValue(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter) {\n  const isActive = ref(true);\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nconst directiveHooks = {\n  mounted: isVue3 ? \"mounted\" : \"inserted\",\n  updated: isVue3 ? \"updated\" : \"componentUpdated\",\n  unmounted: isVue3 ? \"unmounted\" : \"unbind\"\n};\n\nfunction cacheStringFunction(fn) {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\nconst hyphenateRE = /\\B([A-Z])/g;\nconst hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nconst camelizeRE = /-(\\w)/g;\nconst camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?[0-9]+\\.?[0-9]*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = Number.parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction objectOmit(obj, keys, omitUndefined = false) {\n  return Object.fromEntries(Object.entries(obj).filter(([key, value]) => {\n    return (!omitUndefined || value !== void 0) && !keys.includes(key);\n  }));\n}\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\nfunction getLifeCycleTarget(target) {\n  const instance = target || getCurrentInstance();\n  return isVue3 ? instance : instance == null ? void 0 : instance.proxy;\n}\n\nfunction toRef(...args) {\n  if (args.length !== 1)\n    return toRef$1(...args);\n  const r = args[0];\n  return typeof r === \"function\" ? readonly(customRef(() => ({ get: r, set: noop }))) : ref(r);\n}\nconst resolveRef = toRef;\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => predicate(toValue(v), k))) : Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = toValue(defaultValue);\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = toValue(defaultValue);\n      trigger();\n    }, toValue(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(\n    debounceFilter(ms, options),\n    fn\n  );\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(\n    throttleFilter(ms, trailing, leading, rejectOnCancel),\n    fn\n  );\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(\n    ref,\n    {\n      get,\n      set,\n      untrackedGet,\n      silentSet,\n      peek,\n      lay\n    },\n    { enumerable: true }\n  );\n}\nconst controlledRef = refWithControl;\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    if (isVue2) {\n      set$1(...args);\n    } else {\n      const [target, key, value] = args;\n      target[key] = value;\n    }\n  }\n}\n\nfunction watchWithFilter(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  return watch(\n    source,\n    createFilterWrapper(\n      eventFilter,\n      cb\n    ),\n    watchOptions\n  );\n}\n\nfunction watchPausable(source, cb, options = {}) {\n  const {\n    eventFilter: filter,\n    ...watchOptions\n  } = options;\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter);\n  const stop = watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter\n    }\n  );\n  return { stop, pause, resume, isActive };\n}\n\nfunction syncRef(left, right, ...[options]) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options || {};\n  const watchers = [];\n  const transformLTR = \"ltr\" in transform && transform.ltr || ((v) => v);\n  const transformRTL = \"rtl\" in transform && transform.rtl || ((v) => v);\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchers.push(watchPausable(\n      left,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        right.value = transformLTR(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchers.push(watchPausable(\n      right,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        left.value = transformRTL(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  const stop = () => {\n    watchers.forEach((w) => w.stop());\n  };\n  return stop;\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  if (!Array.isArray(targets))\n    targets = [targets];\n  return watch(\n    source,\n    (newValue) => targets.forEach((target) => target.value = newValue),\n    { flush, deep, immediate }\n  );\n}\n\nfunction toRefs(objectRef, options = {}) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        var _a;\n        const replaceRef = (_a = toValue(options.replaceRef)) != null ? _a : true;\n        if (replaceRef) {\n          if (Array.isArray(objectRef.value)) {\n            const copy = [...objectRef.value];\n            copy[key] = v;\n            objectRef.value = copy;\n          } else {\n            const newObject = { ...objectRef.value, [key]: v };\n            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));\n            objectRef.value = newObject;\n          }\n        } else {\n          objectRef.value[key] = v;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nfunction tryOnBeforeMount(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeMount(fn, instance);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeUnmount(fn, instance);\n}\n\nfunction tryOnMounted(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onMounted(fn, instance);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onUnmounted(fn, instance);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        r,\n        (v) => {\n          if (condition(v) !== isNot) {\n            stop == null ? void 0 : stop();\n            resolve(v);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue(r)).finally(() => stop == null ? void 0 : stop())\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        [r, value],\n        ([v1, v2]) => {\n          if (isNot !== (v1 === v2)) {\n            stop == null ? void 0 : stop();\n            resolve(v1);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue(r)).finally(() => {\n          stop == null ? void 0 : stop();\n          return toValue(r);\n        })\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(toValue(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(toValue(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction defaultComparator(value, othVal) {\n  return value === othVal;\n}\nfunction useArrayDifference(...args) {\n  var _a;\n  const list = args[0];\n  const values = args[1];\n  let compareFn = (_a = args[2]) != null ? _a : defaultComparator;\n  if (typeof compareFn === \"string\") {\n    const key = compareFn;\n    compareFn = (value, othVal) => value[key] === othVal[key];\n  }\n  return computed(() => toValue(list).filter((x) => toValue(values).findIndex((y) => compareFn(x, y)) === -1));\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => toValue(list).every((element, index, array) => fn(toValue(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => toValue(list).map((i) => toValue(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => toValue(\n    toValue(list).find((element, index, array) => fn(toValue(element), index, array))\n  ));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => toValue(list).findIndex((element, index, array) => fn(toValue(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => toValue(\n    !Array.prototype.findLast ? findLast(toValue(list), (element, index, array) => fn(toValue(element), index, array)) : toValue(list).findLast((element, index, array) => fn(toValue(element), index, array))\n  ));\n}\n\nfunction isArrayIncludesOptions(obj) {\n  return isObject(obj) && containsProp(obj, \"formIndex\", \"comparator\");\n}\nfunction useArrayIncludes(...args) {\n  var _a;\n  const list = args[0];\n  const value = args[1];\n  let comparator = args[2];\n  let formIndex = 0;\n  if (isArrayIncludesOptions(comparator)) {\n    formIndex = (_a = comparator.fromIndex) != null ? _a : 0;\n    comparator = comparator.comparator;\n  }\n  if (typeof comparator === \"string\") {\n    const key = comparator;\n    comparator = (element, value2) => element[key] === toValue(value2);\n  }\n  comparator = comparator != null ? comparator : (element, value2) => element === toValue(value2);\n  return computed(() => toValue(list).slice(formIndex).some((element, index, array) => comparator(\n    toValue(element),\n    toValue(value),\n    index,\n    toValue(array)\n  )));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => toValue(list).map((i) => toValue(i)).join(toValue(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => toValue(list).map((i) => toValue(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(toValue(sum), toValue(value), index);\n  return computed(() => {\n    const resolved = toValue(list);\n    return args.length ? resolved.reduce(reduceCallback, toValue(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => toValue(list).some((element, index, array) => fn(toValue(element), index, array)));\n}\n\nfunction uniq(array) {\n  return Array.from(new Set(array));\n}\nfunction uniqueElementsBy(array, fn) {\n  return array.reduce((acc, v) => {\n    if (!acc.some((x) => fn(v, x, array)))\n      acc.push(v);\n    return acc;\n  }, []);\n}\nfunction useArrayUnique(list, compareFn) {\n  return computed(() => {\n    const resolvedList = toValue(list).map((element) => toValue(element));\n    return compareFn ? uniqueElementsBy(resolvedList, compareFn) : uniq(resolvedList);\n  });\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  let _initialValue = unref(initialValue);\n  const count = ref(initialValue);\n  const {\n    max = Number.POSITIVE_INFINITY,\n    min = Number.NEGATIVE_INFINITY\n  } = options;\n  const inc = (delta = 1) => count.value = Math.min(max, count.value + delta);\n  const dec = (delta = 1) => count.value = Math.max(min, count.value - delta);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = _initialValue) => {\n    _initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/;\nconst REGEX_FORMAT = /[YMDHhms]o|\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;\nfunction defaultMeridiem(hours, minutes, isLowercase, hasPeriod) {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n}\nfunction formatOrdinal(num) {\n  const suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n  const v = num % 100;\n  return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n}\nfunction formatDate(date, formatStr, options = {}) {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const matches = {\n    Yo: () => formatOrdinal(years),\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    Mo: () => formatOrdinal(month + 1),\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(options.locales, { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(options.locales, { month: \"long\" }),\n    D: () => String(days),\n    Do: () => formatOrdinal(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    Ho: () => formatOrdinal(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    ho: () => formatOrdinal(hours % 12 || 12),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mo: () => formatOrdinal(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    so: () => formatOrdinal(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(options.locales, { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(options.locales, { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(options.locales, { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true)\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => {\n    var _a2, _b;\n    return (_b = $1 != null ? $1 : (_a2 = matches[match]) == null ? void 0 : _a2.call(matches)) != null ? _b : match;\n  });\n}\nfunction normalizeDate(date) {\n  if (date === null)\n    return new Date(Number.NaN);\n  if (date === void 0)\n    return /* @__PURE__ */ new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n}\nfunction useDATE_FORMAT(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(toValue(date)), toValue(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = ref(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = toValue(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || typeof interval === \"function\") {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = ref(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(\n    callback ? () => {\n      update();\n      callback(counter.value);\n    } : update,\n    interval,\n    { immediate }\n  );\n  if (exposeControls) {\n    return {\n      counter,\n      reset,\n      ...controls\n    };\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = ref((_a = options.initialValue) != null ? _a : null);\n  watch(\n    source,\n    () => ms.value = timestamp(),\n    options\n  );\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true\n  } = options;\n  const isPending = ref(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, toValue(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\n\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(\n    callback != null ? callback : noop,\n    interval,\n    options\n  );\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return {\n      ready,\n      ...controls\n    };\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = toValue(value);\n    if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && Number.isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${toValue(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = ref(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = toValue(truthyValue);\n      _value.value = _value.value === truthy ? toValue(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...source instanceof Function ? source() : Array.isArray(source) ? source : toValue(source)];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = Array.from({ length: oldList.length });\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nfunction watchAtMost(source, cb, options) {\n  const {\n    count,\n    ...watchOptions\n  } = options;\n  const current = ref(0);\n  const stop = watchWithFilter(\n    source,\n    (...args) => {\n      current.value += 1;\n      if (current.value >= toValue(count))\n        nextTick(() => stop());\n      cb(...args);\n    },\n    watchOptions\n  );\n  return { count: current, stop };\n}\n\nfunction watchDebounced(source, cb, options = {}) {\n  const {\n    debounce = 0,\n    maxWait = void 0,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: debounceFilter(debounce, { maxWait })\n    }\n  );\n}\n\nfunction watchDeep(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      deep: true\n    }\n  );\n}\n\nfunction watchIgnorable(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  const filteredCb = createFilterWrapper(\n    eventFilter,\n    cb\n  );\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = ref(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(\n      source,\n      (...args) => {\n        if (!ignore.value)\n          filteredCb(...args);\n      },\n      watchOptions\n    );\n  } else {\n    const disposables = [];\n    const ignoreCounter = ref(0);\n    const syncCounter = ref(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(\n      watch(\n        source,\n        () => {\n          syncCounter.value++;\n        },\n        { ...watchOptions, flush: \"sync\" }\n      )\n    );\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(\n      watch(\n        source,\n        (...args) => {\n          const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n          ignoreCounter.value = 0;\n          syncCounter.value = 0;\n          if (ignore)\n            return;\n          filteredCb(...args);\n        },\n        watchOptions\n      )\n    );\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchImmediate(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      immediate: true\n    }\n  );\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n  return stop;\n}\n\nfunction watchThrottled(source, cb, options = {}) {\n  const {\n    throttle = 0,\n    trailing = true,\n    leading = true,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: throttleFilter(throttle, trailing, leading)\n    }\n  );\n}\n\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return {\n    ...res,\n    trigger\n  };\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => toValue(item));\n  return toValue(sources);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  return watch(\n    source,\n    (v, ov, onInvalidate) => {\n      if (v)\n        cb(v, ov, onInvalidate);\n    },\n    options\n  );\n}\n\nexport { assert, refAutoReset as autoResetRef, bypassFilter, camelize, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, directiveHooks, computedEager as eagerComputed, extendRef, formatDate, get, getLifeCycleTarget, hasOwn, hyphenate, identity, watchIgnorable as ignorableWatch, increaseWithUnit, injectLocal, invoke, isClient, isDef, isDefined, isIOS, isObject, isWorker, makeDestructurable, noop, normalizeDate, notNullish, now, objectEntries, objectOmit, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, provideLocal, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toReactive, toRef, toRefs, toValue, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDATE_FORMAT, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchDeep, watchIgnorable, watchImmediate, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, makeDestructurable, camelize, toValue, isClient, isObject, tryOnScopeDispose, isIOS, tryOnMounted, computedWithControl, objectOmit, promiseTimeout, until, increaseWithUnit, objectEntries, createSingletonPromise, useTimeoutFn, pausableWatch, toRef, createEventHook, timestamp, pausableFilter, watchIgnorable, debounceFilter, createFilterWrapper, bypassFilter, toRefs, notNullish, useIntervalFn, containsProp, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, clamp, syncRef, objectPick, tryOnUnmounted, watchWithFilter, tryOnBeforeUnmount, identity, isDef, isWorker } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, ref, shallowRef, watchEffect, computed, inject, isVue3, version, defineComponent, h, TransitionGroup, shallowReactive, Fragment, watch, getCurrentInstance, customRef, onUpdated, onMounted, readonly, nextTick, reactive, markRaw, unref, getCurrentScope, isVue2, set, del, isReadonly, onBeforeUpdate } from 'vue-demi';\nimport { useEventListener as useEventListener$1 } from '@vueuse/core';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    shallow = true,\n    onError = noop\n  } = options;\n  const started = ref(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nfunction createReusableTemplate(options = {}) {\n  if (!isVue3 && !version.startsWith(\"2.7.\")) {\n    if (process.env.NODE_ENV !== \"production\")\n      throw new Error(\"[VueUse] createReusableTemplate only works in Vue 2.7 or above.\");\n    return;\n  }\n  const {\n    inheritAttrs = true\n  } = options;\n  const render = shallowRef();\n  const define = /* #__PURE__ */ defineComponent({\n    setup(_, { slots }) {\n      return () => {\n        render.value = slots.default;\n      };\n    }\n  });\n  const reuse = /* #__PURE__ */ defineComponent({\n    inheritAttrs,\n    setup(_, { attrs, slots }) {\n      return () => {\n        var _a;\n        if (!render.value && process.env.NODE_ENV !== \"production\")\n          throw new Error(\"[VueUse] Failed to find the definition of reusable template\");\n        const vnode = (_a = render.value) == null ? void 0 : _a.call(render, { ...keysToCamelKebabCase(attrs), $slots: slots });\n        return inheritAttrs && (vnode == null ? void 0 : vnode.length) === 1 ? vnode[0] : vnode;\n      };\n    }\n  });\n  return makeDestructurable(\n    { define, reuse },\n    [define, reuse]\n  );\n}\nfunction keysToCamelKebabCase(obj) {\n  const newObj = {};\n  for (const key in obj)\n    newObj[camelize(key)] = obj[key];\n  return newObj;\n}\n\nfunction createTemplatePromise(options = {}) {\n  if (!isVue3) {\n    if (process.env.NODE_ENV !== \"production\")\n      throw new Error(\"[VueUse] createTemplatePromise only works in Vue 3 or above.\");\n    return;\n  }\n  let index = 0;\n  const instances = ref([]);\n  function create(...args) {\n    const props = shallowReactive({\n      key: index++,\n      args,\n      promise: void 0,\n      resolve: () => {\n      },\n      reject: () => {\n      },\n      isResolving: false,\n      options\n    });\n    instances.value.push(props);\n    props.promise = new Promise((_resolve, _reject) => {\n      props.resolve = (v) => {\n        props.isResolving = true;\n        return _resolve(v);\n      };\n      props.reject = _reject;\n    }).finally(() => {\n      props.promise = void 0;\n      const index2 = instances.value.indexOf(props);\n      if (index2 !== -1)\n        instances.value.splice(index2, 1);\n    });\n    return props.promise;\n  }\n  function start(...args) {\n    if (options.singleton && instances.value.length > 0)\n      return instances.value[0].promise;\n    return create(...args);\n  }\n  const component = /* #__PURE__ */ defineComponent((_, { slots }) => {\n    const renderList = () => instances.value.map((props) => {\n      var _a;\n      return h(Fragment, { key: props.key }, (_a = slots.default) == null ? void 0 : _a.call(slots, props));\n    });\n    if (options.transition)\n      return () => h(TransitionGroup, options.transition, renderList);\n    return renderList;\n  });\n  component.start = start;\n  return component;\n}\n\nfunction createUnrefFn(fn) {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => toValue(i)));\n  };\n}\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = toValue(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction useEventListener(...args) {\n  let target;\n  let events;\n  let listeners;\n  let options;\n  if (typeof args[0] === \"string\" || Array.isArray(args[0])) {\n    [events, listeners, options] = args;\n    target = defaultWindow;\n  } else {\n    [target, events, listeners, options] = args;\n  }\n  if (!target)\n    return noop;\n  if (!Array.isArray(events))\n    events = [events];\n  if (!Array.isArray(listeners))\n    listeners = [listeners];\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options2) => {\n    el.addEventListener(event, listener, options2);\n    return () => el.removeEventListener(event, listener, options2);\n  };\n  const stopWatch = watch(\n    () => [unrefElement(target), toValue(options)],\n    ([el, options2]) => {\n      cleanup();\n      if (!el)\n        return;\n      const optionsClone = isObject(options2) ? { ...options2 } : options2;\n      cleanups.push(\n        ...events.flatMap((event) => {\n          return listeners.map((listener) => register(el, event, listener, optionsClone));\n        })\n      );\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false } = options;\n  if (!window)\n    return;\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    Array.from(window.document.body.children).forEach((el) => el.addEventListener(\"click\", noop));\n    window.document.documentElement.addEventListener(\"click\", noop);\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return ignore.some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  const cleanup = [\n    useEventListener(window, \"click\", listener, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      setTimeout(() => {\n        var _a;\n        const el = unrefElement(target);\n        if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement)))\n          handler(event);\n      }, 0);\n    })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nfunction createKeyPredicate(keyFilter) {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n}\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const {\n    target = defaultWindow,\n    eventName = \"keydown\",\n    passive = false,\n    dedupe = false\n  } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (e.repeat && toValue(dedupe))\n      return;\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keydown\" });\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keypress\" });\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keyup\" });\n}\n\nconst DEFAULT_DELAY = 500;\nconst DEFAULT_THRESHOLD = 10;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  let posStart;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n    posStart = void 0;\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    posStart = {\n      x: ev.x,\n      y: ev.y\n    };\n    timeout = setTimeout(\n      () => handler(ev),\n      (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY\n    );\n  }\n  function onMove(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if (!posStart || (options == null ? void 0 : options.distanceThreshold) === false)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - posStart.x;\n    const dy = ev.y - posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    if (distance >= ((_d = options == null ? void 0 : options.distanceThreshold) != null ? _d : DEFAULT_THRESHOLD))\n      clear();\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  const cleanup = [\n    useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions),\n    useEventListener(elementRef, \"pointermove\", onMove, listenerOptions),\n    useEventListener(elementRef, [\"pointerup\", \"pointerleave\"], clear, listenerOptions)\n  ];\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nfunction isFocusedElementEditable() {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n}\nfunction isTypedCharValid({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  if (keyCode >= 97 && keyCode <= 122)\n    return true;\n  return false;\n}\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    !isFocusedElementEditable() && isTypedCharValid(event) && callback(event);\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  var _a;\n  const {\n    window = defaultWindow,\n    deep = true\n  } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const getDeepActiveElement = () => {\n    var _a2;\n    let element = document == null ? void 0 : document.activeElement;\n    if (deep) {\n      while (element == null ? void 0 : element.shadowRoot)\n        element = (_a2 = element == null ? void 0 : element.shadowRoot) == null ? void 0 : _a2.activeElement;\n    }\n    return element;\n  };\n  const activeElement = computedWithControl(\n    () => null,\n    () => getDeepActiveElement()\n  );\n  if (window) {\n    useEventListener(window, \"blur\", (event) => {\n      if (event.relatedTarget !== null)\n        return;\n      activeElement.trigger();\n    }, true);\n    useEventListener(window, \"focus\", activeElement.trigger, true);\n  }\n  return activeElement;\n}\n\nfunction useMounted() {\n  const isMounted = ref(false);\n  if (getCurrentInstance()) {\n    onMounted(() => {\n      isMounted.value = true;\n    });\n  }\n  return isMounted;\n}\n\nfunction useSupported(callback) {\n  const isMounted = useMounted();\n  return computed(() => {\n    isMounted.value;\n    return Boolean(callback());\n  });\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    fpsLimit = void 0,\n    window = defaultWindow\n  } = options;\n  const isActive = ref(false);\n  const intervalLimit = fpsLimit ? 1e3 / fpsLimit : null;\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    const delta = timestamp - (previousFrameTimestamp || timestamp);\n    if (intervalLimit && delta < intervalLimit) {\n      rafId = window.requestAnimationFrame(loop);\n      return;\n    }\n    fn({ delta, timestamp });\n    previousFrameTimestamp = timestamp;\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useAnimate(target, keyframes, options) {\n  let config;\n  let animateOptions;\n  if (isObject(options)) {\n    config = options;\n    animateOptions = objectOmit(options, [\"window\", \"immediate\", \"commitStyles\", \"persist\", \"onReady\", \"onError\"]);\n  } else {\n    config = { duration: options };\n    animateOptions = options;\n  }\n  const {\n    window = defaultWindow,\n    immediate = true,\n    commitStyles,\n    persist,\n    playbackRate: _playbackRate = 1,\n    onReady,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = config;\n  const isSupported = useSupported(() => window && HTMLElement && \"animate\" in HTMLElement.prototype);\n  const animate = shallowRef(void 0);\n  const store = shallowReactive({\n    startTime: null,\n    currentTime: null,\n    timeline: null,\n    playbackRate: _playbackRate,\n    pending: false,\n    playState: immediate ? \"idle\" : \"paused\",\n    replaceState: \"active\"\n  });\n  const pending = computed(() => store.pending);\n  const playState = computed(() => store.playState);\n  const replaceState = computed(() => store.replaceState);\n  const startTime = computed({\n    get() {\n      return store.startTime;\n    },\n    set(value) {\n      store.startTime = value;\n      if (animate.value)\n        animate.value.startTime = value;\n    }\n  });\n  const currentTime = computed({\n    get() {\n      return store.currentTime;\n    },\n    set(value) {\n      store.currentTime = value;\n      if (animate.value) {\n        animate.value.currentTime = value;\n        syncResume();\n      }\n    }\n  });\n  const timeline = computed({\n    get() {\n      return store.timeline;\n    },\n    set(value) {\n      store.timeline = value;\n      if (animate.value)\n        animate.value.timeline = value;\n    }\n  });\n  const playbackRate = computed({\n    get() {\n      return store.playbackRate;\n    },\n    set(value) {\n      store.playbackRate = value;\n      if (animate.value)\n        animate.value.playbackRate = value;\n    }\n  });\n  const play = () => {\n    if (animate.value) {\n      try {\n        animate.value.play();\n        syncResume();\n      } catch (e) {\n        syncPause();\n        onError(e);\n      }\n    } else {\n      update();\n    }\n  };\n  const pause = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.pause();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const reverse = () => {\n    var _a;\n    !animate.value && update();\n    try {\n      (_a = animate.value) == null ? void 0 : _a.reverse();\n      syncResume();\n    } catch (e) {\n      syncPause();\n      onError(e);\n    }\n  };\n  const finish = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.finish();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const cancel = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.cancel();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  watch(() => unrefElement(target), (el) => {\n    el && update();\n  });\n  watch(() => keyframes, (value) => {\n    !animate.value && update();\n    if (!unrefElement(target) && animate.value) {\n      animate.value.effect = new KeyframeEffect(\n        unrefElement(target),\n        toValue(value),\n        animateOptions\n      );\n    }\n  }, { deep: true });\n  tryOnMounted(() => {\n    nextTick(() => update(true));\n  });\n  tryOnScopeDispose(cancel);\n  function update(init) {\n    const el = unrefElement(target);\n    if (!isSupported.value || !el)\n      return;\n    animate.value = el.animate(toValue(keyframes), animateOptions);\n    if (commitStyles)\n      animate.value.commitStyles();\n    if (persist)\n      animate.value.persist();\n    if (_playbackRate !== 1)\n      animate.value.playbackRate = _playbackRate;\n    if (init && !immediate)\n      animate.value.pause();\n    else\n      syncResume();\n    onReady == null ? void 0 : onReady(animate.value);\n  }\n  useEventListener(animate, [\"cancel\", \"finish\", \"remove\"], syncPause);\n  const { resume: resumeRef, pause: pauseRef } = useRafFn(() => {\n    if (!animate.value)\n      return;\n    store.pending = animate.value.pending;\n    store.playState = animate.value.playState;\n    store.replaceState = animate.value.replaceState;\n    store.startTime = animate.value.startTime;\n    store.currentTime = animate.value.currentTime;\n    store.timeline = animate.value.timeline;\n    store.playbackRate = animate.value.playbackRate;\n  }, { immediate: false });\n  function syncResume() {\n    if (isSupported.value)\n      resumeRef();\n  }\n  function syncPause() {\n    if (isSupported.value && window)\n      window.requestAnimationFrame(pauseRef);\n  }\n  return {\n    isSupported,\n    animate,\n    // actions\n    play,\n    pause,\n    reverse,\n    finish,\n    cancel,\n    // state\n    pending,\n    playState,\n    replaceState,\n    startTime,\n    currentTime,\n    timeline,\n    playbackRate\n  };\n}\n\nfunction useAsyncQueue(tasks, options) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop,\n    signal\n  } = options || {};\n  const promiseState = {\n    aborted: \"aborted\",\n    fulfilled: \"fulfilled\",\n    pending: \"pending\",\n    rejected: \"rejected\"\n  };\n  const initialResult = Array.from(Array.from({ length: tasks.length }), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = ref(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, new Error(\"aborted\"));\n        return;\n      }\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      const done = curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        activeIndex.value === tasks.length - 1 && onFinished();\n        return currentRes;\n      });\n      if (!signal)\n        return done;\n      return Promise.race([done, whenAborted(signal)]);\n    }).catch((e) => {\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, e);\n        return e;\n      }\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\nfunction whenAborted(signal) {\n  return new Promise((resolve, reject) => {\n    const error = new Error(\"aborted\");\n    if (signal.aborted)\n      reject(error);\n    else\n      signal.addEventListener(\"abort\", () => reject(error), { once: true });\n  });\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = ref(false);\n  const isLoading = ref(false);\n  const error = shallowRef(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw e;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate)\n    execute(delay);\n  const shell = {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n  function waitUntilIsLoaded() {\n    return new Promise((resolve, reject) => {\n      until(isLoading).toBe(false).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilIsLoaded().then(onFulfilled, onRejected);\n    }\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = ref(\"\");\n  const promise = ref();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = toValue(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => base64.value = res);\n    return promise.value;\n  }\n  if (isRef(target) || typeof target === \"function\")\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useBattery(options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator);\n  const charging = ref(false);\n  const chargingTime = ref(0);\n  const dischargingTime = ref(0);\n  const level = ref(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      useEventListener(battery, events, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef(void 0);\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = ref();\n  const isConnected = computed(() => {\n    var _a;\n    return ((_a = server.value) == null ? void 0 : _a.connected) || false;\n  });\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      device.value.addEventListener(\"gattserverdisconnected\", () => {\n      });\n      try {\n        server.value = await device.value.gatt.connect();\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected,\n    // Device:\n    device,\n    requestDevice,\n    // Server:\n    server,\n    // Errors:\n    error\n  };\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  let mediaQuery;\n  const matches = ref(false);\n  const handler = (event) => {\n    matches.value = event.matches;\n  };\n  const cleanup = () => {\n    if (!mediaQuery)\n      return;\n    if (\"removeEventListener\" in mediaQuery)\n      mediaQuery.removeEventListener(\"change\", handler);\n    else\n      mediaQuery.removeListener(handler);\n  };\n  const stopWatch = watchEffect(() => {\n    if (!isSupported.value)\n      return;\n    cleanup();\n    mediaQuery = window.matchMedia(toValue(query));\n    if (\"addEventListener\" in mediaQuery)\n      mediaQuery.addEventListener(\"change\", handler);\n    else\n      mediaQuery.addListener(handler);\n    matches.value = mediaQuery.matches;\n  });\n  tryOnScopeDispose(() => {\n    stopWatch();\n    cleanup();\n    mediaQuery = void 0;\n  });\n  return matches;\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetify = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904\n};\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 600,\n  sm: 1024,\n  md: 1440,\n  lg: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\nconst breakpointsPrimeFlex = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\n\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = toValue(breakpoints[k]);\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow } = options;\n  function match(query) {\n    if (!window)\n      return false;\n    return window.matchMedia(query).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(() => `(min-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => greaterOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  return Object.assign(shortcutMethods, {\n    greater(k) {\n      return useMediaQuery(() => `(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    greaterOrEqual,\n    smaller(k) {\n      return useMediaQuery(() => `(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    smallerOrEqual(k) {\n      return useMediaQuery(() => `(max-width: ${getValue(k)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(() => `(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(`(min-width: ${getValue(k, 0.1)})`);\n    },\n    isGreaterOrEqual(k) {\n      return match(`(min-width: ${getValue(k)})`);\n    },\n    isSmaller(k) {\n      return match(`(max-width: ${getValue(k, -0.1)})`);\n    },\n    isSmallerOrEqual(k) {\n      return match(`(max-width: ${getValue(k)})`);\n    },\n    isInBetween(a, b) {\n      return match(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`);\n    },\n    current() {\n      const points = Object.keys(breakpoints).map((i) => [i, greaterOrEqual(i)]);\n      return computed(() => points.filter(([, v]) => v.value).map(([k]) => k));\n    }\n  });\n}\n\nfunction useBroadcastChannel(options) {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = ref(false);\n  const channel = ref();\n  const data = ref();\n  const error = shallowRef(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      channel.value.addEventListener(\"message\", (e) => {\n        data.value = e.data;\n      }, { passive: true });\n      channel.value.addEventListener(\"messageerror\", (e) => {\n        error.value = e;\n      }, { passive: true });\n      channel.value.addEventListener(\"close\", () => {\n        isClosed.value = true;\n      });\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n}\n\nconst WRITABLE_PROPERTIES = [\n  \"hash\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"search\"\n];\nfunction useBrowserLocation(options = {}) {\n  const { window = defaultWindow } = options;\n  const refs = Object.fromEntries(\n    WRITABLE_PROPERTIES.map((key) => [key, ref()])\n  );\n  for (const [key, ref2] of objectEntries(refs)) {\n    watch(ref2, (value) => {\n      if (!(window == null ? void 0 : window.location) || window.location[key] === value)\n        return;\n      window.location[key] = value;\n    });\n  }\n  const buildState = (trigger) => {\n    var _a;\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { origin } = (window == null ? void 0 : window.location) || {};\n    for (const key of WRITABLE_PROPERTIES)\n      refs[key].value = (_a = window == null ? void 0 : window.location) == null ? void 0 : _a[key];\n    return reactive({\n      trigger,\n      state: state2,\n      length,\n      origin,\n      ...refs\n    });\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), { passive: true });\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), { passive: true });\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, watchOptions) {\n  const cachedValue = ref(refValue.value);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  let permissionStatus;\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = ref();\n  const onChange = () => {\n    if (permissionStatus)\n      state.value = permissionStatus.state;\n  };\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus) {\n      try {\n        permissionStatus = await navigator.permissions.query(desc);\n        useEventListener(permissionStatus, \"change\", onChange);\n        onChange();\n      } catch (e) {\n        state.value = \"prompt\";\n      }\n    }\n    return permissionStatus;\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const permissionRead = usePermission(\"clipboard-read\");\n  const permissionWrite = usePermission(\"clipboard-write\");\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = ref(\"\");\n  const copied = ref(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring);\n  function updateText() {\n    if (isClipboardApiSupported.value && permissionRead.value !== \"denied\") {\n      navigator.clipboard.readText().then((value) => {\n        text.value = value;\n      });\n    } else {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateText);\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      if (isClipboardApiSupported.value && permissionWrite.value !== \"denied\")\n        await navigator.clipboard.writeText(value);\n      else\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nfunction useClipboardItems(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500\n  } = options;\n  const isSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const content = ref([]);\n  const copied = ref(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring);\n  function updateContent() {\n    if (isSupported.value) {\n      navigator.clipboard.read().then((items) => {\n        content.value = items;\n      });\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateContent);\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      await navigator.clipboard.write(value);\n      content.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  return {\n    isSupported,\n    content,\n    copied,\n    copy\n  };\n}\n\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const {\n    manual,\n    clone = cloneFnJSON,\n    // watch options\n    deep = true,\n    immediate = true\n  } = options;\n  function sync() {\n    cloned.value = clone(toValue(source));\n  }\n  if (!manual && (isRef(source) || typeof source === \"function\")) {\n    watch(source, sync, {\n      ...options,\n      deep,\n      immediate\n    });\n  } else {\n    sync();\n  }\n  return { cloned, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\nconst handlers = /* @__PURE__ */ getHandlers();\nfunction getHandlers() {\n  if (!(globalKey in _global))\n    _global[globalKey] = _global[globalKey] || {};\n  return _global[globalKey];\n}\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    initOnMounted\n  } = options;\n  const data = (shallow ? shallowRef : ref)(typeof defaults === \"function\" ? defaults() : defaults);\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = toValue(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(\n    data,\n    () => write(data.value),\n    { flush, deep, eventFilter }\n  );\n  if (window && listenToStorageChanges) {\n    tryOnMounted(() => {\n      useEventListener(window, \"storage\", update);\n      useEventListener(window, customStorageEventName, updateFromCustomEvent);\n      if (initOnMounted)\n        update();\n    });\n  }\n  if (!initOnMounted)\n    update();\n  return data;\n  function write(v) {\n    try {\n      if (v == null) {\n        storage.removeItem(key);\n      } else {\n        const serialized = serializer.write(v);\n        const oldValue = storage.getItem(key);\n        if (oldValue !== serialized) {\n          storage.setItem(key, serialized);\n          if (window) {\n            window.dispatchEvent(new CustomEvent(customStorageEventName, {\n              detail: {\n                key,\n                oldValue,\n                newValue: serialized,\n                storageArea: storage\n              }\n            }));\n          }\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(key);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit != null)\n        storage.setItem(key, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (typeof mergeDefaults === \"function\")\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return { ...rawInit, ...value };\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== key)\n      return;\n    pauseWatch();\n    try {\n      if ((event == null ? void 0 : event.newValue) !== serializer.write(data.value))\n        data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto,\n    disableTransition = true\n  } = options;\n  const modes = {\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\",\n    ...options.modes || {}\n  };\n  const preferredDark = usePreferredDark({ window });\n  const system = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? toRef(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed(() => store.value === \"auto\" ? system.value : store.value);\n  const updateHTMLAttrs = getSSRHandler(\n    \"updateHTMLAttrs\",\n    (selector2, attribute2, value) => {\n      const el = typeof selector2 === \"string\" ? window == null ? void 0 : window.document.querySelector(selector2) : unrefElement(selector2);\n      if (!el)\n        return;\n      let style;\n      if (disableTransition) {\n        style = window.document.createElement(\"style\");\n        const styleString = \"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\";\n        style.appendChild(document.createTextNode(styleString));\n        window.document.head.appendChild(style);\n      }\n      if (attribute2 === \"class\") {\n        const current = value.split(/\\s/g);\n        Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n          if (current.includes(v))\n            el.classList.add(v);\n          else\n            el.classList.remove(v);\n        });\n      } else {\n        el.setAttribute(attribute2, value);\n      }\n      if (disableTransition) {\n        window.getComputedStyle(style).opacity;\n        document.head.removeChild(style);\n      }\n    }\n  );\n  function defaultOnChanged(mode) {\n    var _a;\n    updateHTMLAttrs(selector, attribute, (_a = modes[mode]) != null ? _a : mode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  const auto = computed({\n    get() {\n      return emitAuto ? store.value : state.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  try {\n    return Object.assign(auto, { store, system, state });\n  } catch (e) {\n    return auto;\n  }\n}\n\nfunction useConfirmDialog(revealed = ref(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useMutationObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...mutationOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(\n    () => unrefElement(target),\n    (el) => {\n      cleanup();\n      if (isSupported.value && window && el) {\n        observer = new MutationObserver(callback);\n        observer.observe(el, mutationOptions);\n      }\n    },\n    { immediate: true }\n  );\n  const takeRecords = () => {\n    return observer == null ? void 0 : observer.takeRecords();\n  };\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop,\n    takeRecords\n  };\n}\n\nfunction useCssVar(prop, target, options = {}) {\n  const { window = defaultWindow, initialValue = \"\", observe = false } = options;\n  const variable = ref(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  function updateCssVar() {\n    var _a;\n    const key = toValue(prop);\n    const el = toValue(elRef);\n    if (el && window) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(key)) == null ? void 0 : _a.trim();\n      variable.value = value || initialValue;\n    }\n  }\n  if (observe) {\n    useMutationObserver(elRef, updateCssVar, {\n      attributeFilter: [\"style\", \"class\"],\n      window\n    });\n  }\n  watch(\n    [elRef, () => toValue(prop)],\n    updateCssVar,\n    { immediate: true }\n  );\n  watch(\n    variable,\n    (val) => {\n      var _a;\n      if ((_a = elRef.value) == null ? void 0 : _a.style)\n        elRef.value.style.setProperty(toValue(prop), val);\n    }\n  );\n  return variable;\n}\n\nfunction useCurrentElement() {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(\n    () => null,\n    () => vm.proxy.$el\n  );\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  const state = shallowRef(getInitialValue());\n  const listRef = toRef(list);\n  const index = computed({\n    get() {\n      var _a;\n      const targetList = listRef.value;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, targetList) : targetList.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a = options == null ? void 0 : options.fallbackIndex) != null ? _a : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const targetList = listRef.value;\n    const length = targetList.length;\n    const index2 = (i % length + length) % length;\n    const value = targetList[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  function getInitialValue() {\n    var _a, _b;\n    return (_b = toValue((_a = options == null ? void 0 : options.initialValue) != null ? _a : toValue(list)[0])) != null ? _b : void 0;\n  }\n  watch(listRef, () => set(index.value));\n  return {\n    state,\n    index,\n    next,\n    prev\n  };\n}\n\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\",\n    window = defaultWindow\n  } = options;\n  const mode = useColorMode({\n    ...options,\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\", defaultHandler, mode2);\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  });\n  const system = computed(() => {\n    if (mode.system) {\n      return mode.system.value;\n    } else {\n      const preferredDark = usePreferredDark({ window });\n      return preferredDark.value ? \"dark\" : \"light\";\n    }\n  });\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      const modeVal = v ? \"dark\" : \"light\";\n      if (system.value === modeVal)\n        mode.value = \"auto\";\n      else\n        mode.value = modeVal;\n    }\n  });\n  return isDark;\n}\n\nfunction fnBypass(v) {\n  return v;\n}\nfunction fnSetSource(source, value) {\n  return source.value = value;\n}\nfunction defaultDump(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Number.POSITIVE_INFINITY);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(\n    source,\n    commit,\n    { deep, flush, eventFilter: composedFilter }\n  );\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, { ...options, clone: options.clone || deep, setSource });\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return {\n    ...manualHistory,\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  };\n}\n\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    eventFilter = bypassFilter\n  } = options;\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = ref(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  if (window) {\n    const onDeviceMotion = createFilterWrapper(\n      eventFilter,\n      (event) => {\n        acceleration.value = event.acceleration;\n        accelerationIncludingGravity.value = event.accelerationIncludingGravity;\n        rotationRate.value = event.rotationRate;\n        interval.value = event.interval;\n      }\n    );\n    useEventListener(window, \"devicemotion\", onDeviceMotion);\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = ref(false);\n  const alpha = ref(null);\n  const beta = ref(null);\n  const gamma = ref(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nfunction useDevicePixelRatio(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const pixelRatio = ref(1);\n  if (window) {\n    let observe2 = function() {\n      pixelRatio.value = window.devicePixelRatio;\n      cleanup2();\n      media = window.matchMedia(`(resolution: ${pixelRatio.value}dppx)`);\n      media.addEventListener(\"change\", observe2, { once: true });\n    }, cleanup2 = function() {\n      media == null ? void 0 : media.removeEventListener(\"change\", observe2);\n    };\n    let media;\n    observe2();\n    tryOnScopeDispose(cleanup2);\n  }\n  return { pixelRatio };\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = ref(false);\n  let stream;\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n    if (stream) {\n      stream.getTracks().forEach((t) => t.stop());\n      stream = null;\n    }\n  }\n  async function ensurePermissions() {\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(\"camera\", { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      stream = await navigator.mediaDevices.getUserMedia(constraints);\n      update();\n      permissionGranted.value = true;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update);\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else\n        _stop();\n    },\n    { immediate: true }\n  );\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility(options = {}) {\n  const { document = defaultDocument } = options;\n  if (!document)\n    return ref(\"visible\");\n  const visibility = ref(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  });\n  return visibility;\n}\n\nfunction useDraggable(target, options = {}) {\n  var _a, _b;\n  const {\n    pointerTypes,\n    preventDefault,\n    stopPropagation,\n    exact,\n    onMove,\n    onEnd,\n    onStart,\n    initialValue,\n    axis = \"both\",\n    draggingElement = defaultWindow,\n    containerElement,\n    handle: draggingHandle = target\n  } = options;\n  const position = ref(\n    (_a = toValue(initialValue)) != null ? _a : { x: 0, y: 0 }\n  );\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (pointerTypes)\n      return pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (toValue(preventDefault))\n      e.preventDefault();\n    if (toValue(stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (toValue(exact) && e.target !== toValue(target))\n      return;\n    const container = toValue(containerElement);\n    const containerRect = (_a2 = container == null ? void 0 : container.getBoundingClientRect) == null ? void 0 : _a2.call(container);\n    const targetRect = toValue(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - (container ? targetRect.left - containerRect.left : targetRect.left),\n      y: e.clientY - (container ? targetRect.top - containerRect.top : targetRect.top)\n    };\n    if ((onStart == null ? void 0 : onStart(pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    const container = toValue(containerElement);\n    const containerRect = (_a2 = container == null ? void 0 : container.getBoundingClientRect) == null ? void 0 : _a2.call(container);\n    const targetRect = toValue(target).getBoundingClientRect();\n    let { x, y } = position.value;\n    if (axis === \"x\" || axis === \"both\") {\n      x = e.clientX - pressedDelta.value.x;\n      if (container)\n        x = Math.min(Math.max(0, x), containerRect.width - targetRect.width);\n    }\n    if (axis === \"y\" || axis === \"both\") {\n      y = e.clientY - pressedDelta.value.y;\n      if (container)\n        y = Math.min(Math.max(0, y), containerRect.height - targetRect.height);\n    }\n    position.value = {\n      x,\n      y\n    };\n    onMove == null ? void 0 : onMove(position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    onEnd == null ? void 0 : onEnd(position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    const config = { capture: (_b = options.capture) != null ? _b : true };\n    useEventListener(draggingHandle, \"pointerdown\", start, config);\n    useEventListener(draggingElement, \"pointermove\", move, config);\n    useEventListener(draggingElement, \"pointerup\", end, config);\n  }\n  return {\n    ...toRefs(position),\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(\n      () => `left:${position.value.x}px;top:${position.value.y}px;`\n    )\n  };\n}\n\nfunction useDropZone(target, options = {}) {\n  const isOverDropZone = ref(false);\n  const files = shallowRef(null);\n  let counter = 0;\n  let isDataTypeIncluded = true;\n  if (isClient) {\n    const _options = typeof options === \"function\" ? { onDrop: options } : options;\n    const getFiles = (event) => {\n      var _a, _b;\n      const list = Array.from((_b = (_a = event.dataTransfer) == null ? void 0 : _a.files) != null ? _b : []);\n      return files.value = list.length === 0 ? null : list;\n    };\n    useEventListener$1(target, \"dragenter\", (event) => {\n      var _a, _b;\n      const types = Array.from(((_a = event == null ? void 0 : event.dataTransfer) == null ? void 0 : _a.items) || []).map((i) => i.kind === \"file\" ? i.type : null).filter(notNullish);\n      if (_options.dataTypes && event.dataTransfer) {\n        const dataTypes = unref(_options.dataTypes);\n        isDataTypeIncluded = typeof dataTypes === \"function\" ? dataTypes(types) : dataTypes ? dataTypes.some((item) => types.includes(item)) : true;\n        if (!isDataTypeIncluded)\n          return;\n      }\n      event.preventDefault();\n      counter += 1;\n      isOverDropZone.value = true;\n      (_b = _options.onEnter) == null ? void 0 : _b.call(_options, getFiles(event), event);\n    });\n    useEventListener$1(target, \"dragover\", (event) => {\n      var _a;\n      if (!isDataTypeIncluded)\n        return;\n      event.preventDefault();\n      (_a = _options.onOver) == null ? void 0 : _a.call(_options, getFiles(event), event);\n    });\n    useEventListener$1(target, \"dragleave\", (event) => {\n      var _a;\n      if (!isDataTypeIncluded)\n        return;\n      event.preventDefault();\n      counter -= 1;\n      if (counter === 0)\n        isOverDropZone.value = false;\n      (_a = _options.onLeave) == null ? void 0 : _a.call(_options, getFiles(event), event);\n    });\n    useEventListener$1(target, \"drop\", (event) => {\n      var _a;\n      event.preventDefault();\n      counter = 0;\n      isOverDropZone.value = false;\n      (_a = _options.onDrop) == null ? void 0 : _a.call(_options, getFiles(event), event);\n    });\n  }\n  return {\n    files,\n    isOverDropZone\n  };\n}\n\nfunction useResizeObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...observerOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => Array.isArray(target) ? target.map((el) => unrefElement(el)) : [unrefElement(target)]);\n  const stopWatch = watch(\n    targets,\n    (els) => {\n      cleanup();\n      if (isSupported.value && window) {\n        observer = new ResizeObserver(callback);\n        for (const _el of els)\n          _el && observer.observe(_el, observerOptions);\n      }\n    },\n    { immediate: true, flush: \"post\", deep: true }\n  );\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true\n  } = options;\n  const height = ref(0);\n  const bottom = ref(0);\n  const left = ref(0);\n  const right = ref(0);\n  const top = ref(0);\n  const width = ref(0);\n  const x = ref(0);\n  const y = ref(0);\n  function update() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useElementByPoint(options) {\n  const {\n    x,\n    y,\n    document = defaultDocument,\n    multiple,\n    interval = \"requestAnimationFrame\",\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => {\n    if (toValue(multiple))\n      return document && \"elementsFromPoint\" in document;\n    return document && \"elementFromPoint\" in document;\n  });\n  const element = ref(null);\n  const cb = () => {\n    var _a, _b;\n    element.value = toValue(multiple) ? (_a = document == null ? void 0 : document.elementsFromPoint(toValue(x), toValue(y))) != null ? _a : [] : (_b = document == null ? void 0 : document.elementFromPoint(toValue(x), toValue(y))) != null ? _b : null;\n  };\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  return {\n    isSupported,\n    element,\n    ...controls\n  };\n}\n\nfunction useElementHover(el, options = {}) {\n  const {\n    delayEnter = 0,\n    delayLeave = 0,\n    window = defaultWindow\n  } = options;\n  const isHovered = ref(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = ref(initialSize.width);\n  const height = ref(initialSize.height);\n  const { stop: stop1 } = useResizeObserver(\n    target,\n    ([entry]) => {\n      const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n      if (window && isSVG.value) {\n        const $elem = unrefElement(target);\n        if ($elem) {\n          const styles = window.getComputedStyle($elem);\n          width.value = Number.parseFloat(styles.width);\n          height.value = Number.parseFloat(styles.height);\n        }\n      } else {\n        if (boxSize) {\n          const formatBoxSize = Array.isArray(boxSize) ? boxSize : [boxSize];\n          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n        } else {\n          width.value = entry.contentRect.width;\n          height.value = entry.contentRect.height;\n        }\n      }\n    },\n    options\n  );\n  tryOnMounted(() => {\n    const ele = unrefElement(target);\n    if (ele) {\n      width.value = \"offsetWidth\" in ele ? ele.offsetWidth : initialSize.width;\n      height.value = \"offsetHeight\" in ele ? ele.offsetHeight : initialSize.height;\n    }\n  });\n  const stop2 = watch(\n    () => unrefElement(target),\n    (ele) => {\n      width.value = ele ? initialSize.width : 0;\n      height.value = ele ? initialSize.height : 0;\n    }\n  );\n  function stop() {\n    stop1();\n    stop2();\n  }\n  return {\n    width,\n    height,\n    stop\n  };\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0.1,\n    window = defaultWindow,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  const targets = computed(() => {\n    const _target = toValue(target);\n    return (Array.isArray(_target) ? _target : [_target]).map(unrefElement).filter(notNullish);\n  });\n  let cleanup = noop;\n  const isActive = ref(immediate);\n  const stopWatch = isSupported.value ? watch(\n    () => [targets.value, unrefElement(root), isActive.value],\n    ([targets2, root2]) => {\n      cleanup();\n      if (!isActive.value)\n        return;\n      if (!targets2.length)\n        return;\n      const observer = new IntersectionObserver(\n        callback,\n        {\n          root: unrefElement(root2),\n          rootMargin,\n          threshold\n        }\n      );\n      targets2.forEach((el) => el && observer.observe(el));\n      cleanup = () => {\n        observer.disconnect();\n        cleanup = noop;\n      };\n    },\n    { immediate, flush: \"post\" }\n  ) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n    isActive.value = false;\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    isActive,\n    pause() {\n      cleanup();\n      isActive.value = false;\n    },\n    resume() {\n      isActive.value = true;\n    },\n    stop\n  };\n}\n\nfunction useElementVisibility(element, options = {}) {\n  const { window = defaultWindow, scrollTarget } = options;\n  const elementIsVisible = ref(false);\n  useIntersectionObserver(\n    element,\n    (intersectionObserverEntries) => {\n      let isIntersecting = elementIsVisible.value;\n      let latestTime = 0;\n      for (const entry of intersectionObserverEntries) {\n        if (entry.time >= latestTime) {\n          latestTime = entry.time;\n          isIntersecting = entry.isIntersecting;\n        }\n      }\n      elementIsVisible.value = isIntersecting;\n    },\n    {\n      root: scrollTarget,\n      window,\n      threshold: 0\n    }\n  );\n  return elementIsVisible;\n}\n\nconst events = /* @__PURE__ */ new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || /* @__PURE__ */ new Set();\n    listeners.add(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    listeners.delete(listener);\n    if (!listeners.size)\n      reset();\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction useEventSource(url, events = [], options = {}) {\n  const event = ref(null);\n  const data = ref(null);\n  const status = ref(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = shallowRef(null);\n  const {\n    withCredentials = false\n  } = options;\n  const close = () => {\n    if (eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n    }\n  };\n  const es = new EventSource(url, { withCredentials });\n  eventSource.value = es;\n  es.onopen = () => {\n    status.value = \"OPEN\";\n    error.value = null;\n  };\n  es.onerror = (e) => {\n    status.value = \"CLOSED\";\n    error.value = e;\n  };\n  es.onmessage = (e) => {\n    event.value = null;\n    data.value = e.data;\n  };\n  for (const event_name of events) {\n    useEventListener(es, event_name, (e) => {\n      event.value = event_name;\n      data.value = e.data || null;\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    close\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = ref(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = toRef(newIcon);\n  const applyIcon = (icon) => {\n    const elements = document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`);\n    if (!elements || elements.length === 0) {\n      const link = document == null ? void 0 : document.createElement(\"link\");\n      if (link) {\n        link.rel = rel;\n        link.href = `${baseUrl}${icon}`;\n        link.type = `image/${icon.split(\".\").pop()}`;\n        document == null ? void 0 : document.head.append(link);\n      }\n      return;\n    }\n    elements == null ? void 0 : elements.forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(\n    favicon,\n    (i, o) => {\n      if (typeof i === \"string\" && i !== o)\n        applyIcon(i);\n    },\n    { immediate: true }\n  );\n  return favicon;\n}\n\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\", \"updateDataOnError\");\n}\nfunction isAbsoluteURL(url) {\n  return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries([...headers.entries()]);\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      const callback = callbacks[callbacks.length - 1];\n      if (callback)\n        return { ...ctx, ...await callback(ctx) };\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      for (const callback of callbacks) {\n        if (callback)\n          ctx = { ...ctx, ...await callback(ctx) };\n      }\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = toValue(config.baseUrl);\n      const targetUrl = toValue(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = {\n          ...options,\n          ...args[0],\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        };\n      } else {\n        fetchOptions = {\n          ...fetchOptions,\n          ...args[0],\n          headers: {\n            ...headersToObject(fetchOptions.headers) || {},\n            ...headersToObject(args[0].headers) || {}\n          }\n        };\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = {\n        ...options,\n        ...args[1],\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      };\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = {\n    immediate: true,\n    refetch: false,\n    timeout: 0,\n    updateDataOnError: false\n  };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = { ...options, ...args[0] };\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = { ...options, ...args[1] };\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = ref(false);\n  const isFetching = ref(false);\n  const aborted = ref(false);\n  const statusCode = ref(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData || null);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort) {\n      controller == null ? void 0 : controller.abort();\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = {\n        ...fetchOptions,\n        signal: controller.signal\n      };\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  let executeCounter = 0;\n  const execute = async (throwOnFailed = false) => {\n    var _a2, _b;\n    abort();\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    executeCounter += 1;\n    const currentExecuteCounter = executeCounter;\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    if (config.payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      const payload = toValue(config.payload);\n      if (!config.payloadType && payload && Object.getPrototypeOf(payload) === Object.prototype && !(payload instanceof FormData))\n        config.payloadType = \"json\";\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: toValue(url),\n      options: {\n        ...defaultFetchOptions,\n        ...fetchOptions\n      },\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return fetch(\n      context.url,\n      {\n        ...defaultFetchOptions,\n        ...context.options,\n        headers: {\n          ...headersToObject(defaultFetchOptions.headers),\n          ...headersToObject((_b = context.options) == null ? void 0 : _b.headers)\n        }\n      }\n    ).then(async (fetchResponse) => {\n      response.value = fetchResponse;\n      statusCode.value = fetchResponse.status;\n      responseData = await fetchResponse.clone()[config.type]();\n      if (!fetchResponse.ok) {\n        data.value = initialData || null;\n        throw new Error(fetchResponse.statusText);\n      }\n      if (options.afterFetch) {\n        ({ data: responseData } = await options.afterFetch({\n          data: responseData,\n          response: fetchResponse\n        }));\n      }\n      data.value = responseData;\n      responseEvent.trigger(fetchResponse);\n      return fetchResponse;\n    }).catch(async (fetchError) => {\n      let errorData = fetchError.message || fetchError.name;\n      if (options.onFetchError) {\n        ({ error: errorData, data: responseData } = await options.onFetchError({\n          data: responseData,\n          error: fetchError,\n          response: response.value\n        }));\n      }\n      error.value = errorData;\n      if (options.updateDataOnError)\n        data.value = responseData;\n      errorEvent.trigger(fetchError);\n      if (throwOnFailed)\n        throw fetchError;\n      return null;\n    }).finally(() => {\n      if (currentExecuteCounter === executeCounter)\n        loading(false);\n      if (timer)\n        timer.stop();\n      finallyEvent.trigger(null);\n    });\n  };\n  const refetch = toRef(options.refetch);\n  watch(\n    [\n      refetch,\n      toRef(url)\n    ],\n    ([refetch2]) => refetch2 && execute(),\n    { deep: true }\n  );\n  const shell = {\n    isFinished,\n    statusCode,\n    response,\n    error,\n    data,\n    isFetching,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    // method\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    // type\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch(\n            [\n              refetch,\n              toRef(config.payload)\n            ],\n            ([refetch2]) => refetch2 && execute(),\n            { deep: true }\n          );\n        }\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch((error2) => reject(error2));\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    Promise.resolve().then(() => execute());\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  };\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\"))\n    return `${start}/${end}`;\n  return `${start}${end}`;\n}\n\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\",\n  reset: false,\n  directory: false\n};\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(null);\n  const { on: onChange, trigger } = createEventHook();\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n      trigger(files.value);\n    };\n  }\n  const reset = () => {\n    files.value = null;\n    if (input) {\n      input.value = \"\";\n      trigger(null);\n    }\n  };\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n      ...localOptions\n    };\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    input.webkitdirectory = _options.directory;\n    if (hasOwn(_options, \"capture\"))\n      input.capture = _options.capture;\n    if (_options.reset)\n      reset();\n    input.click();\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset,\n    onChange\n  };\n}\n\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = options;\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = ref();\n  const data = ref();\n  const file = ref();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker({ ...toValue(options), ..._options });\n    fileHandle.value = handle;\n    await updateFile();\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    data.value = void 0;\n    await updateFile();\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    const type = toValue(dataType);\n    if (type === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    else if (type === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    else if (type === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => toValue(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false, focusVisible = false } = options;\n  const innerFocused = ref(false);\n  const targetElement = computed(() => unrefElement(target));\n  useEventListener(targetElement, \"focus\", (event) => {\n    var _a, _b;\n    if (!focusVisible || ((_b = (_a = event.target).matches) == null ? void 0 : _b.call(_a, \":focus-visible\")))\n      innerFocused.value = true;\n  });\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus();\n    }\n  });\n  watch(\n    targetElement,\n    () => {\n      focused.value = initialValue;\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  return { focused };\n}\n\nfunction useFocusWithin(target, options = {}) {\n  const activeElement = useActiveElement(options);\n  const targetElement = computed(() => unrefElement(target));\n  const focused = computed(() => targetElement.value && activeElement.value ? targetElement.value.contains(activeElement.value) : false);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = ref(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst eventHandlers = [\n  \"fullscreenchange\",\n  \"webkitfullscreenchange\",\n  \"webkitendfullscreen\",\n  \"mozfullscreenchange\",\n  \"MSFullscreenChange\"\n];\nfunction useFullscreen(target, options = {}) {\n  const {\n    document = defaultDocument,\n    autoExit = false\n  } = options;\n  const targetRef = computed(() => {\n    var _a;\n    return (_a = unrefElement(target)) != null ? _a : document == null ? void 0 : document.querySelector(\"html\");\n  });\n  const isFullscreen = ref(false);\n  const requestMethod = computed(() => {\n    return [\n      \"requestFullscreen\",\n      \"webkitRequestFullscreen\",\n      \"webkitEnterFullscreen\",\n      \"webkitEnterFullScreen\",\n      \"webkitRequestFullScreen\",\n      \"mozRequestFullScreen\",\n      \"msRequestFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const exitMethod = computed(() => {\n    return [\n      \"exitFullscreen\",\n      \"webkitExitFullscreen\",\n      \"webkitExitFullScreen\",\n      \"webkitCancelFullScreen\",\n      \"mozCancelFullScreen\",\n      \"msExitFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenEnabled = computed(() => {\n    return [\n      \"fullScreen\",\n      \"webkitIsFullScreen\",\n      \"webkitDisplayingFullscreen\",\n      \"mozFullScreen\",\n      \"msFullscreenElement\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenElementMethod = [\n    \"fullscreenElement\",\n    \"webkitFullscreenElement\",\n    \"mozFullScreenElement\",\n    \"msFullscreenElement\"\n  ].find((m) => document && m in document);\n  const isSupported = useSupported(() => targetRef.value && document && requestMethod.value !== void 0 && exitMethod.value !== void 0 && fullscreenEnabled.value !== void 0);\n  const isCurrentElementFullScreen = () => {\n    if (fullscreenElementMethod)\n      return (document == null ? void 0 : document[fullscreenElementMethod]) === targetRef.value;\n    return false;\n  };\n  const isElementFullScreen = () => {\n    if (fullscreenEnabled.value) {\n      if (document && document[fullscreenEnabled.value] != null) {\n        return document[fullscreenEnabled.value];\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[fullscreenEnabled.value]) != null) {\n          return Boolean(target2[fullscreenEnabled.value]);\n        }\n      }\n    }\n    return false;\n  };\n  async function exit() {\n    if (!isSupported.value || !isFullscreen.value)\n      return;\n    if (exitMethod.value) {\n      if ((document == null ? void 0 : document[exitMethod.value]) != null) {\n        await document[exitMethod.value]();\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[exitMethod.value]) != null)\n          await target2[exitMethod.value]();\n      }\n    }\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value || isFullscreen.value)\n      return;\n    if (isElementFullScreen())\n      await exit();\n    const target2 = targetRef.value;\n    if (requestMethod.value && (target2 == null ? void 0 : target2[requestMethod.value]) != null) {\n      await target2[requestMethod.value]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    await (isFullscreen.value ? exit() : enter());\n  }\n  const handlerCallback = () => {\n    const isElementFullScreenValue = isElementFullScreen();\n    if (!isElementFullScreenValue || isElementFullScreenValue && isCurrentElementFullScreen())\n      isFullscreen.value = isElementFullScreenValue;\n  };\n  useEventListener(document, eventHandlers, handlerCallback, false);\n  useEventListener(() => unrefElement(targetRef), eventHandlers, handlerCallback, false);\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      ...gamepad,\n      id: gamepad.id,\n      hapticActuators,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (let i = 0; i < _gamepads.length; ++i) {\n      const gamepad = _gamepads[i];\n      if (gamepad) {\n        const index = gamepads.value.findIndex(({ index: index2 }) => index2 === gamepad.index);\n        if (index > -1)\n          gamepads.value[index] = stateFromGamepad(gamepad);\n      }\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad));\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad));\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    if (_gamepads) {\n      for (let i = 0; i < _gamepads.length; ++i) {\n        const gamepad = _gamepads[i];\n        if (gamepad)\n          onGamepadConnected(gamepad);\n      }\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = ref(null);\n  const error = shallowRef(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Number.POSITIVE_INFINITY,\n    longitude: Number.POSITIVE_INFINITY,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(\n        updatePosition,\n        (err) => error.value = err,\n        {\n          enableHighAccuracy,\n          maximumAge,\n          timeout\n        }\n      );\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = ref(initialState);\n  const lastActive = ref(timestamp());\n  let timer;\n  const reset = () => {\n    idle.value = false;\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  };\n  const onEvent = createFilterWrapper(\n    eventFilter,\n    () => {\n      lastActive.value = timestamp();\n      reset();\n    }\n  );\n  if (window) {\n    const document = window.document;\n    for (const event of events)\n      useEventListener(window, event, onEvent, { passive: true });\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      });\n    }\n    reset();\n  }\n  return {\n    idle,\n    lastActive,\n    reset\n  };\n}\n\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes, class: clazz, loading, crossorigin, referrerPolicy } = options;\n    img.src = src;\n    if (srcset)\n      img.srcset = srcset;\n    if (sizes)\n      img.sizes = sizes;\n    if (clazz)\n      img.className = clazz;\n    if (loading)\n      img.loading = loading;\n    if (crossorigin)\n      img.crossOrigin = crossorigin;\n    if (referrerPolicy)\n      img.referrerPolicy = referrerPolicy;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nfunction useImage(options, asyncStateOptions = {}) {\n  const state = useAsyncState(\n    () => loadImage(toValue(options)),\n    void 0,\n    {\n      resetOnExecute: true,\n      ...asyncStateOptions\n    }\n  );\n  watch(\n    () => toValue(options),\n    () => state.execute(asyncStateOptions.delay),\n    { deep: true }\n  );\n  return state;\n}\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\",\n    window = defaultWindow\n  } = options;\n  const internalX = ref(0);\n  const internalY = ref(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c;\n    if (!window)\n      return;\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? window.document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = toValue(_y)) != null ? _a : y.value,\n      left: (_b = toValue(_x)) != null ? _b : x.value,\n      behavior: toValue(behavior)\n    });\n  }\n  const isScrolling = ref(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const setArrivedState = (target) => {\n    var _a;\n    if (!window)\n      return;\n    const el = target.document ? target.document.documentElement : (_a = target.documentElement) != null ? _a : target;\n    const { display, flexDirection } = getComputedStyle(el);\n    const scrollLeft = el.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalX.value;\n    const left = Math.abs(scrollLeft) <= 0 + (offset.left || 0);\n    const right = Math.abs(scrollLeft) + el.clientWidth >= el.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"row-reverse\") {\n      arrivedState.left = right;\n      arrivedState.right = left;\n    } else {\n      arrivedState.left = left;\n      arrivedState.right = right;\n    }\n    internalX.value = scrollLeft;\n    let scrollTop = el.scrollTop;\n    if (target === window.document && !scrollTop)\n      scrollTop = window.document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    const top = Math.abs(scrollTop) <= 0 + (offset.top || 0);\n    const bottom = Math.abs(scrollTop) + el.clientHeight >= el.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"column-reverse\") {\n      arrivedState.top = bottom;\n      arrivedState.bottom = top;\n    } else {\n      arrivedState.top = top;\n      arrivedState.bottom = bottom;\n    }\n    internalY.value = scrollTop;\n  };\n  const onScrollHandler = (e) => {\n    var _a;\n    if (!window)\n      return;\n    const eventTarget = (_a = e.target.documentElement) != null ? _a : e.target;\n    setArrivedState(eventTarget);\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(\n    element,\n    \"scroll\",\n    throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler,\n    eventListenerOptions\n  );\n  tryOnMounted(() => {\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    setArrivedState(_element);\n  });\n  useEventListener(\n    element,\n    \"scrollend\",\n    onScrollEnd,\n    eventListenerOptions\n  );\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions,\n    measure() {\n      const _element = toValue(element);\n      if (window && _element)\n        setArrivedState(_element);\n    }\n  };\n}\n\nfunction resolveElement(el) {\n  if (typeof Window !== \"undefined\" && el instanceof Window)\n    return el.document.documentElement;\n  if (typeof Document !== \"undefined\" && el instanceof Document)\n    return el.documentElement;\n  return el;\n}\n\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a;\n  const {\n    direction = \"bottom\",\n    interval = 100,\n    canLoadMore = () => true\n  } = options;\n  const state = reactive(useScroll(\n    element,\n    {\n      ...options,\n      offset: {\n        [direction]: (_a = options.distance) != null ? _a : 0,\n        ...options.offset\n      }\n    }\n  ));\n  const promise = ref();\n  const isLoading = computed(() => !!promise.value);\n  const observedElement = computed(() => {\n    return resolveElement(toValue(element));\n  });\n  const isElementVisible = useElementVisibility(observedElement);\n  function checkAndLoad() {\n    state.measure();\n    if (!observedElement.value || !isElementVisible.value || !canLoadMore(observedElement.value))\n      return;\n    const { scrollHeight, clientHeight, scrollWidth, clientWidth } = observedElement.value;\n    const isNarrower = direction === \"bottom\" || direction === \"top\" ? scrollHeight <= clientHeight : scrollWidth <= clientWidth;\n    if (state.arrivedState[direction] || isNarrower) {\n      if (!promise.value) {\n        promise.value = Promise.all([\n          onLoadMore(state),\n          new Promise((resolve) => setTimeout(resolve, interval))\n        ]).finally(() => {\n          promise.value = null;\n          nextTick(() => checkAndLoad());\n        });\n      }\n    }\n  }\n  watch(\n    () => [state.arrivedState[direction], isElementVisible.value],\n    checkAndLoad,\n    { immediate: true }\n  );\n  return {\n    isLoading\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = ref(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(/* @__PURE__ */ new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = /* @__PURE__ */ new Set();\n  const usedKeys = /* @__PURE__ */ new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive: true });\n  useEventListener(\"focus\", reset, { passive: true });\n  const proxy = new Proxy(\n    refs,\n    {\n      get(target2, prop, rec) {\n        if (typeof prop !== \"string\")\n          return Reflect.get(target2, prop, rec);\n        prop = prop.toLowerCase();\n        if (prop in aliasMap)\n          prop = aliasMap[prop];\n        if (!(prop in refs)) {\n          if (/[+_-]/.test(prop)) {\n            const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n            refs[prop] = computed(() => keys.every((key) => toValue(proxy[key])));\n          } else {\n            refs[prop] = ref(false);\n          }\n        }\n        const r = Reflect.get(target2, prop, rec);\n        return useReactive ? toValue(r) : r;\n      }\n    }\n  );\n  return proxy;\n}\n\nfunction usingElRef(source, cb) {\n  if (toValue(source))\n    cb(toValue(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  options = {\n    ...defaultOptions,\n    ...options\n  };\n  const {\n    document = defaultDocument\n  } = options;\n  const currentTime = ref(0);\n  const duration = ref(0);\n  const seeking = ref(false);\n  const volume = ref(1);\n  const waiting = ref(false);\n  const ended = ref(false);\n  const playing = ref(false);\n  const rate = ref(1);\n  const stalled = ref(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = ref(-1);\n  const isPictureInPicture = ref(false);\n  const muted = ref(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = typeof track === \"number\" ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = typeof track === \"number\" ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = toValue(target);\n    if (!el)\n      return;\n    const src = toValue(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (typeof src === \"string\")\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.removeEventListener(\"error\", sourceErrorEvent.trigger);\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.addEventListener(\"error\", sourceErrorEvent.trigger);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  tryOnScopeDispose(() => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.querySelectorAll(\"source\").forEach((e) => e.removeEventListener(\"error\", sourceErrorEvent.trigger));\n  });\n  watch([target, volume], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.volume = volume.value;\n  });\n  watch([target, muted], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.muted = muted.value;\n  });\n  watch([target, rate], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.playbackRate = rate.value;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = toValue(options.tracks);\n    const el = toValue(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    isPlaying ? el.play() : el.pause();\n  });\n  useEventListener(target, \"timeupdate\", () => ignoreCurrentTimeUpdates(() => currentTime.value = toValue(target).currentTime));\n  useEventListener(target, \"durationchange\", () => duration.value = toValue(target).duration);\n  useEventListener(target, \"progress\", () => buffered.value = timeRangeToArray(toValue(target).buffered));\n  useEventListener(target, \"seeking\", () => seeking.value = true);\n  useEventListener(target, \"seeked\", () => seeking.value = false);\n  useEventListener(target, [\"waiting\", \"loadstart\"], () => {\n    waiting.value = true;\n    ignorePlayingUpdates(() => playing.value = false);\n  });\n  useEventListener(target, \"loadeddata\", () => waiting.value = false);\n  useEventListener(target, \"playing\", () => {\n    waiting.value = false;\n    ended.value = false;\n    ignorePlayingUpdates(() => playing.value = true);\n  });\n  useEventListener(target, \"ratechange\", () => rate.value = toValue(target).playbackRate);\n  useEventListener(target, \"stalled\", () => stalled.value = true);\n  useEventListener(target, \"ended\", () => ended.value = true);\n  useEventListener(target, \"pause\", () => ignorePlayingUpdates(() => playing.value = false));\n  useEventListener(target, \"play\", () => ignorePlayingUpdates(() => playing.value = true));\n  useEventListener(target, \"enterpictureinpicture\", () => isPictureInPicture.value = true);\n  useEventListener(target, \"leavepictureinpicture\", () => isPictureInPicture.value = false);\n  useEventListener(target, \"volumechange\", () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    volume.value = el.volume;\n    muted.value = el.muted;\n  });\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks));\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    // Volume\n    volume,\n    muted,\n    // Tracks\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    // Picture in Picture\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    // Events\n    onSourceError: sourceErrorEvent.on\n  };\n}\n\nfunction getMapVue2Compat() {\n  const data = shallowReactive({});\n  return {\n    get: (key) => data[key],\n    set: (key, value) => set(data, key, value),\n    has: (key) => hasOwn(data, key),\n    delete: (key) => del(data, key),\n    clear: () => {\n      Object.keys(data).forEach((key) => {\n        del(data, key);\n      });\n    }\n  };\n}\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return shallowReactive(options.cache);\n    if (isVue2)\n      return getMapVue2Compat();\n    return shallowReactive(/* @__PURE__ */ new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nconst UseMouseBuiltinExtractors = {\n  page: (event) => [event.pageX, event.pageY],\n  client: (event) => [event.clientX, event.clientY],\n  screen: (event) => [event.screenX, event.screenY],\n  movement: (event) => event instanceof Touch ? null : [event.movementX, event.movementY]\n};\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    target = window,\n    scroll = true,\n    eventFilter\n  } = options;\n  let _prevMouseEvent = null;\n  const x = ref(initialValue.x);\n  const y = ref(initialValue.y);\n  const sourceType = ref(null);\n  const extractor = typeof type === \"function\" ? type : UseMouseBuiltinExtractors[type];\n  const mouseHandler = (event) => {\n    const result = extractor(event);\n    _prevMouseEvent = event;\n    if (result) {\n      [x.value, y.value] = result;\n      sourceType.value = \"mouse\";\n    }\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const result = extractor(event.touches[0]);\n      if (result) {\n        [x.value, y.value] = result;\n        sourceType.value = \"touch\";\n      }\n    }\n  };\n  const scrollHandler = () => {\n    if (!_prevMouseEvent || !window)\n      return;\n    const pos = extractor(_prevMouseEvent);\n    if (_prevMouseEvent instanceof MouseEvent && pos) {\n      x.value = pos[0] + window.scrollX;\n      y.value = pos[1] + window.scrollY;\n    }\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const mouseHandlerWrapper = eventFilter ? (event) => eventFilter(() => mouseHandler(event), {}) : (event) => mouseHandler(event);\n  const touchHandlerWrapper = eventFilter ? (event) => eventFilter(() => touchHandler(event), {}) : (event) => touchHandler(event);\n  const scrollHandlerWrapper = eventFilter ? () => eventFilter(() => scrollHandler(), {}) : () => scrollHandler();\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"mousemove\", \"dragover\"], mouseHandlerWrapper, listenerOptions);\n    if (touch && type !== \"movement\") {\n      useEventListener(target, [\"touchstart\", \"touchmove\"], touchHandlerWrapper, listenerOptions);\n      if (resetOnTouchEnds)\n        useEventListener(target, \"touchend\", reset, listenerOptions);\n    }\n    if (scroll && type === \"page\")\n      useEventListener(window, \"scroll\", scrollHandlerWrapper, { passive: true });\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const type = options.type || \"page\";\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = ref(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = ref(0);\n  const elementY = ref(0);\n  const elementPositionX = ref(0);\n  const elementPositionY = ref(0);\n  const elementHeight = ref(0);\n  const elementWidth = ref(0);\n  const isOutside = ref(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch(\n      [targetRef, x, y],\n      () => {\n        const el = unrefElement(targetRef);\n        if (!el)\n          return;\n        const {\n          left,\n          top,\n          width,\n          height\n        } = el.getBoundingClientRect();\n        elementPositionX.value = left + (type === \"page\" ? window.pageXOffset : 0);\n        elementPositionY.value = top + (type === \"page\" ? window.pageYOffset : 0);\n        elementHeight.value = height;\n        elementWidth.value = width;\n        const elX = x.value - elementPositionX.value;\n        const elY = y.value - elementPositionY.value;\n        isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n        if (handleOutside || !isOutside.value) {\n          elementX.value = elX;\n          elementY.value = elY;\n        }\n      },\n      { immediate: true }\n    );\n    useEventListener(document, \"mouseleave\", () => {\n      isOutside.value = true;\n    });\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    capture = false,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = ref(initialValue);\n  const sourceType = ref(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => () => {\n    pressed.value = true;\n    sourceType.value = srcType;\n  };\n  const onReleased = () => {\n    pressed.value = false;\n    sourceType.value = null;\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), { passive: true, capture });\n  useEventListener(window, \"mouseleave\", onReleased, { passive: true, capture });\n  useEventListener(window, \"mouseup\", onReleased, { passive: true, capture });\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), { passive: true, capture });\n    useEventListener(window, \"drop\", onReleased, { passive: true, capture });\n    useEventListener(window, \"dragend\", onReleased, { passive: true, capture });\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), { passive: true, capture });\n    useEventListener(window, \"touchend\", onReleased, { passive: true, capture });\n    useEventListener(window, \"touchcancel\", onReleased, { passive: true, capture });\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nfunction useNavigatorLanguage(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = ref(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  });\n  return {\n    isSupported,\n    language\n  };\n}\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = ref(true);\n  const saveData = ref(false);\n  const offlineAt = ref(void 0);\n  const onlineAt = ref(void 0);\n  const downlink = ref(void 0);\n  const downlinkMax = ref(void 0);\n  const rtt = ref(void 0);\n  const effectiveType = ref(void 0);\n  const type = ref(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    });\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    });\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, false);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline,\n    saveData,\n    offlineAt,\n    onlineAt,\n    downlink,\n    downlinkMax,\n    effectiveType,\n    rtt,\n    type\n  };\n}\n\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(/* @__PURE__ */ new Date());\n  const update = () => now.value = /* @__PURE__ */ new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return {\n      now,\n      ...controls\n    };\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = ref();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(\n    () => toValue(object),\n    (newObject) => {\n      release();\n      if (newObject)\n        url.value = URL.createObjectURL(newObject);\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useClamp(value, min, max) {\n  if (typeof value === \"function\" || isReadonly(value))\n    return computed(() => clamp(toValue(value), toValue(min), toValue(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, toValue(min), toValue(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, toValue(min), toValue(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Number.POSITIVE_INFINITY,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Number.POSITIVE_INFINITY);\n  const pageCount = computed(() => Math.max(\n    1,\n    Math.ceil(toValue(total) / toValue(currentPageSize))\n  ));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page)) {\n    syncRef(page, currentPage, {\n      direction: isReadonly(page) ? \"ltr\" : \"both\"\n    });\n  }\n  if (isRef(pageSize)) {\n    syncRef(pageSize, currentPageSize, {\n      direction: isReadonly(pageSize) ? \"ltr\" : \"both\"\n    });\n  }\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = ref(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    useEventListener(window, \"mouseout\", handler, { passive: true });\n    useEventListener(window.document, \"mouseleave\", handler, { passive: true });\n    useEventListener(window.document, \"mouseenter\", handler, { passive: true });\n  }\n  return isLeft;\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0))\n      return \"deviceOrientation\";\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = -orientation.beta / 90;\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = orientation.gamma / 90;\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nfunction useParentElement(element = useCurrentElement()) {\n  const parentElement = shallowRef();\n  const update = () => {\n    const el = unrefElement(element);\n    if (el)\n      parentElement.value = el.parentElement;\n  };\n  tryOnMounted(update);\n  watch(() => toValue(element), update);\n  return parentElement;\n}\n\nfunction usePerformanceObserver(options, callback) {\n  const {\n    window = defaultWindow,\n    immediate = true,\n    ...performanceOptions\n  } = options;\n  const isSupported = useSupported(() => window && \"PerformanceObserver\" in window);\n  let observer;\n  const stop = () => {\n    observer == null ? void 0 : observer.disconnect();\n  };\n  const start = () => {\n    if (isSupported.value) {\n      stop();\n      observer = new PerformanceObserver(callback);\n      observer.observe(performanceOptions);\n    }\n  };\n  tryOnScopeDispose(stop);\n  if (immediate)\n    start();\n  return {\n    isSupported,\n    start,\n    stop\n  };\n}\n\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = ref(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"pointerdown\", \"pointermove\", \"pointerup\"], handler, listenerOptions);\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, listenerOptions);\n  }\n  return {\n    ...toRefs(state),\n    isInside\n  };\n}\n\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument, pointerLockOptions } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = ref();\n  const triggerElement = ref();\n  let targetElement;\n  if (isSupported.value) {\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    });\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    });\n  }\n  async function lock(e, options2) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock(options2 != null ? options2 : pointerLockOptions);\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = toRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    disableTextSelect = false\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const isPointerDown = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return distanceY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }),\n    useEventListener(target, \"pointerup\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n    })\n  ];\n  tryOnMounted(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n    if (disableTextSelect) {\n      (_d = (_c = targetRef.value) == null ? void 0 : _c.style) == null ? void 0 : _d.setProperty(\"-webkit-user-select\", \"none\");\n      (_f = (_e = targetRef.value) == null ? void 0 : _e.style) == null ? void 0 : _f.setProperty(\"-ms-user-select\", \"none\");\n      (_h = (_g = targetRef.value) == null ? void 0 : _g.style) == null ? void 0 : _h.setProperty(\"user-select\", \"none\");\n    }\n  });\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  });\n  return value;\n}\n\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(\n    toRef(value),\n    (_, oldValue) => {\n      previous.value = oldValue;\n    },\n    { flush: \"sync\" }\n  );\n  return readonly(previous);\n}\n\nfunction useScreenOrientation(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = ref(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    });\n  }\n  const lockOrientation = (type) => {\n    if (isSupported.value && typeof screenOrientation.lock === \"function\")\n      return screenOrientation.lock(type);\n    return Promise.reject(new Error(\"Not supported\"));\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value && typeof screenOrientation.unlock === \"function\")\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n}\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = ref(\"\");\n  const right = ref(\"\");\n  const bottom = ref(\"\");\n  const left = ref(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update));\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = ref(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = toValue(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    el.addEventListener(\"error\", (event) => reject(event));\n    el.addEventListener(\"abort\", (event) => reject(event));\n    el.addEventListener(\"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    });\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientWidth < ele.scrollWidth || style.overflowY === \"auto\" && ele.clientHeight < ele.scrollHeight) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nconst elInitialOverflow = /* @__PURE__ */ new WeakMap();\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = ref(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow;\n  watch(toRef(element), (el) => {\n    const target = resolveElement(toValue(el));\n    if (target) {\n      const ele = target;\n      if (!elInitialOverflow.get(ele))\n        elInitialOverflow.set(ele, initialOverflow);\n      if (isLocked.value)\n        ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        el,\n        \"touchmove\",\n        (e) => {\n          preventDefault(e);\n        },\n        { passive: false }\n      );\n    }\n    el.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    var _a;\n    const el = resolveElement(toValue(element));\n    if (!el || !isLocked.value)\n      return;\n    isIOS && (stopTouchMoveListener == null ? void 0 : stopTouchMoveListener());\n    el.style.overflow = (_a = elInitialOverflow.get(el)) != null ? _a : \"\";\n    elInitialOverflow.delete(el);\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else\n        unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = {\n        ...toValue(shareOptions),\n        ...toValue(overrideOptions)\n      };\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...toValue(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(toValue(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    window = defaultWindow\n  } = options;\n  const lang = toRef(options.lang || \"en-US\");\n  const isListening = ref(false);\n  const isFinal = ref(false);\n  const result = ref(\"\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isListening.value) => {\n    isListening.value = value;\n  };\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  let recognition;\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = toValue(lang);\n    recognition.onstart = () => {\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const transcript = Array.from(event.results).map((result2) => {\n        isFinal.value = result2.isFinal;\n        return result2[0];\n      }).map((result2) => result2.transcript).join(\"\");\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = toValue(lang);\n    };\n    watch(isListening, () => {\n      if (isListening.value)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isListening.value = false;\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = ref(false);\n  const status = ref(\"init\");\n  const spokenText = toRef(text || \"\");\n  const lang = toRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = toValue(lang);\n    utterance2.voice = toValue(options.voice) || null;\n    utterance2.pitch = toValue(pitch);\n    utterance2.rate = toValue(rate);\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    utterance && synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = toValue(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(initialValue);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (typeof mergeDefaults === \"function\")\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = { ...rawInit, ...value };\n        else\n          data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => Promise.resolve().then(() => read(e)));\n  if (storage) {\n    watchWithFilter(\n      data,\n      async () => {\n        try {\n          if (data.value == null)\n            await storage.removeItem(key);\n          else\n            await storage.setItem(key, await serializer.write(data.value));\n        } catch (e) {\n          onError(e);\n        }\n      },\n      {\n        flush,\n        deep,\n        eventFilter\n      }\n    );\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = ref(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = ref(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.id = id;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(\n      cssRef,\n      (value) => {\n        el.textContent = value;\n      },\n      { immediate: true }\n    );\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true,\n    window = defaultWindow\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return diffY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  let listenerOptions;\n  const isPassiveEventSupported = checkPassiveEventSupport(window == null ? void 0 : window.document);\n  if (!passive)\n    listenerOptions = isPassiveEventSupported ? { passive: false, capture: true } : { capture: true };\n  else\n    listenerOptions = isPassiveEventSupported ? { passive: true } : { capture: false };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      if (listenerOptions.capture && !listenerOptions.passive)\n        e.preventDefault();\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, [\"touchend\", \"touchcancel\"], onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isPassiveEventSupported,\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop\n  };\n}\nfunction checkPassiveEventSupport(document) {\n  if (!document)\n    return false;\n  let supportsPassive = false;\n  const optionsBlock = {\n    get passive() {\n      supportsPassive = true;\n      return false;\n    }\n  };\n  document.addEventListener(\"x\", noop, optionsBlock);\n  document.removeEventListener(\"x\", noop);\n  return supportsPassive;\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(\n      document.querySelector(selector),\n      () => dir.value = getValue(),\n      { attributes: true }\n    );\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  return Array.from({ length: rangeCount }, (_, i) => selection.getRangeAt(i));\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange);\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction useTextareaAutosize(options) {\n  const textarea = ref(options == null ? void 0 : options.element);\n  const input = ref(options == null ? void 0 : options.input);\n  const textareaScrollHeight = ref(1);\n  function triggerResize() {\n    var _a, _b;\n    if (!textarea.value)\n      return;\n    let height = \"\";\n    textarea.value.style.height = \"1px\";\n    textareaScrollHeight.value = (_a = textarea.value) == null ? void 0 : _a.scrollHeight;\n    if (options == null ? void 0 : options.styleTarget)\n      toValue(options.styleTarget).style.height = `${textareaScrollHeight.value}px`;\n    else\n      height = `${textareaScrollHeight.value}px`;\n    textarea.value.style.height = height;\n    (_b = options == null ? void 0 : options.onResize) == null ? void 0 : _b.call(options);\n  }\n  watch([input, textarea], () => nextTick(triggerResize), { immediate: true });\n  useResizeObserver(textarea, () => triggerResize());\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nfunction DEFAULT_FORMATTER(date) {\n  return date.toISOString().slice(0, 10);\n}\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const { now, ...controls } = useNow({ interval: updateInterval, controls: true });\n  const timeAgo = computed(() => formatTimeAgo(new Date(toValue(time)), options, toValue(now)));\n  if (exposeControls) {\n    return {\n      timeAgo,\n      ...controls\n    };\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDATE_FORMATter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDATE_FORMATter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDATE_FORMATter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nfunction useTimeoutPoll(fn, interval, timeoutPollOptions) {\n  const { start } = useTimeoutFn(loop, interval, { immediate: false });\n  const isActive = ref(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      loop();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (timeoutPollOptions == null ? void 0 : timeoutPollOptions.immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = ref(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return {\n      timestamp: ts,\n      ...controls\n    };\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b, _c;\n  const {\n    document = defaultDocument,\n    restoreOnUnmount = (t) => t\n  } = options;\n  const originalTitle = (_a = document == null ? void 0 : document.title) != null ? _a : \"\";\n  const title = toRef((_b = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _b : null);\n  const isReadonly = newTitle && typeof newTitle === \"function\";\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return typeof template === \"function\" ? template(t) : toValue(template).replace(/%s/g, t);\n  }\n  watch(\n    title,\n    (t, o) => {\n      if (t !== o && document)\n        document.title = format(typeof t === \"string\" ? t : \"\");\n    },\n    { immediate: true }\n  );\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver(\n      (_c = document.head) == null ? void 0 : _c.querySelector(\"title\"),\n      () => {\n        if (document && document.title !== title.value)\n          title.value = format(document.title);\n      },\n      { childList: true }\n    );\n  }\n  tryOnBeforeUnmount(() => {\n    if (restoreOnUnmount) {\n      const restoredTitle = restoreOnUnmount(originalTitle, title.value || \"\");\n      if (restoredTitle != null && document)\n        document.title = restoredTitle;\n    }\n  });\n  return title;\n}\n\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = /* @__PURE__ */ Object.assign({}, { linear: identity }, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction lerp(a, b, alpha) {\n  return a + alpha * (b - a);\n}\nfunction toVec(t) {\n  return (typeof t === \"number\" ? [t] : t) || [];\n}\nfunction executeTransition(source, from, to, options = {}) {\n  var _a, _b;\n  const fromVal = toValue(from);\n  const toVal = toValue(to);\n  const v1 = toVec(fromVal);\n  const v2 = toVec(toVal);\n  const duration = (_a = toValue(options.duration)) != null ? _a : 1e3;\n  const startedAt = Date.now();\n  const endAt = Date.now() + duration;\n  const trans = typeof options.transition === \"function\" ? options.transition : (_b = toValue(options.transition)) != null ? _b : identity;\n  const ease = typeof trans === \"function\" ? trans : createEasingFunction(trans);\n  return new Promise((resolve) => {\n    source.value = fromVal;\n    const tick = () => {\n      var _a2;\n      if ((_a2 = options.abort) == null ? void 0 : _a2.call(options)) {\n        resolve();\n        return;\n      }\n      const now = Date.now();\n      const alpha = ease((now - startedAt) / duration);\n      const arr = toVec(source.value).map((n, i) => lerp(v1[i], v2[i], alpha));\n      if (Array.isArray(source.value))\n        source.value = arr.map((n, i) => {\n          var _a3, _b2;\n          return lerp((_a3 = v1[i]) != null ? _a3 : 0, (_b2 = v2[i]) != null ? _b2 : 0, alpha);\n        });\n      else if (typeof source.value === \"number\")\n        source.value = arr[0];\n      if (now < endAt) {\n        requestAnimationFrame(tick);\n      } else {\n        source.value = toVal;\n        resolve();\n      }\n    };\n    tick();\n  });\n}\nfunction useTransition(source, options = {}) {\n  let currentId = 0;\n  const sourceVal = () => {\n    const v = toValue(source);\n    return typeof v === \"number\" ? v : v.map(toValue);\n  };\n  const outputRef = ref(sourceVal());\n  watch(sourceVal, async (to) => {\n    var _a, _b;\n    if (toValue(options.disabled))\n      return;\n    const id = ++currentId;\n    if (options.delay)\n      await promiseTimeout(toValue(options.delay));\n    if (id !== currentId)\n      return;\n    const toVal = Array.isArray(to) ? to.map(toValue) : toValue(to);\n    (_a = options.onStarted) == null ? void 0 : _a.call(options);\n    await executeTransition(outputRef, outputRef.value, toVal, {\n      ...options,\n      abort: () => {\n        var _a2;\n        return id !== currentId || ((_a2 = options.abort) == null ? void 0 : _a2.call(options));\n      }\n    });\n    (_b = options.onFinished) == null ? void 0 : _b.call(options);\n  }, { deep: true });\n  watch(() => toValue(options.disabled), (disabled) => {\n    if (disabled) {\n      currentId++;\n      outputRef.value = sourceVal();\n    }\n  });\n  tryOnScopeDispose(() => {\n    currentId++;\n  });\n  return computed(() => toValue(options.disabled) ? sourceVal() : outputRef.value);\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(\n    state,\n    () => {\n      const params = new URLSearchParams(\"\");\n      Object.keys(state).forEach((key) => {\n        const mapEntry = state[key];\n        if (Array.isArray(mapEntry))\n          mapEntry.forEach((value) => params.append(key, value));\n        else if (removeNullishValues && mapEntry == null)\n          params.delete(key);\n        else if (removeFalsyValues && !mapEntry)\n          params.delete(key);\n        else\n          params.set(key, mapEntry);\n      });\n      write(params);\n    },\n    { deep: true }\n  );\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    window.history.replaceState(\n      window.history.state,\n      window.document.title,\n      window.location.pathname + constructQuery(params)\n    );\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  useEventListener(window, \"popstate\", onChanged, false);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, false);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = ref((_b = options.autoSwitch) != null ? _b : true);\n  const constraints = ref(options.constraints);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(type) {\n    switch (type) {\n      case \"video\": {\n        if (constraints.value)\n          return constraints.value.video || false;\n        break;\n      }\n      case \"audio\": {\n        if (constraints.value)\n          return constraints.value.audio || false;\n        break;\n      }\n    }\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(\"video\"),\n      audio: getDeviceOptions(\"audio\")\n    });\n    return stream.value;\n  }\n  function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else\n        _stop();\n    },\n    { immediate: true }\n  );\n  watch(\n    constraints,\n    () => {\n      if (autoSwitch.value && stream.value)\n        restart();\n    },\n    { immediate: true }\n  );\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    constraints,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c, _d, _e;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue,\n    shouldEmit\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    if (isVue2) {\n      const modelOptions = (_e = (_d = vm == null ? void 0 : vm.proxy) == null ? void 0 : _d.$options) == null ? void 0 : _e.model;\n      key = (modelOptions == null ? void 0 : modelOptions.value) || \"value\";\n      if (!eventName)\n        event = (modelOptions == null ? void 0 : modelOptions.event) || \"input\";\n    } else {\n      key = \"modelValue\";\n    }\n  }\n  event = event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : typeof clone === \"function\" ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  const triggerEmit = (value) => {\n    if (shouldEmit) {\n      if (shouldEmit(value))\n        _emit(event, value);\n    } else {\n      _emit(event, value);\n    }\n  };\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    let isUpdating = false;\n    watch(\n      () => props[key],\n      (v) => {\n        if (!isUpdating) {\n          isUpdating = true;\n          proxy.value = cloneFn(v);\n          nextTick(() => isUpdating = false);\n        }\n      }\n    );\n    watch(\n      proxy,\n      (v) => {\n        if (!isUpdating && (v !== props[key] || deep))\n          triggerEmit(v);\n      },\n      { deep }\n    );\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        triggerEmit(value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props) {\n    ret[key] = useVModel(\n      props,\n      key,\n      emit,\n      options\n    );\n  }\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = toRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(\n      vibrate,\n      interval,\n      {\n        immediate: false,\n        immediateCallback: false\n      }\n    );\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = ref(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, calculateRange) {\n  watch([size.width, size.height, list], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\nfunction useWakeLock(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  let wakeLock;\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = ref(false);\n  async function onVisibilityChange() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    if (document && document.visibilityState === \"visible\")\n      wakeLock = await navigator.wakeLock.request(\"screen\");\n    isActive.value = !wakeLock.released;\n  }\n  if (document)\n    useEventListener(document, \"visibilitychange\", onVisibilityChange, { passive: true });\n  async function request(type) {\n    if (!isSupported.value)\n      return;\n    wakeLock = await navigator.wakeLock.request(type);\n    isActive.value = !wakeLock.released;\n  }\n  async function release() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    await wakeLock.release();\n    isActive.value = !wakeLock.released;\n    wakeLock = null;\n  }\n  return {\n    isSupported,\n    isActive,\n    request,\n    release\n  };\n}\n\nfunction useWebNotification(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions: _requestForPermissions = true\n  } = options;\n  const defaultWebNotificationOptions = options;\n  const isSupported = useSupported(() => !!window && \"Notification\" in window);\n  const permissionGranted = ref(isSupported.value && \"permission\" in Notification && Notification.permission === \"granted\");\n  const notification = ref(null);\n  const ensurePermissions = async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionGranted.value && Notification.permission !== \"denied\") {\n      const result = await Notification.requestPermission();\n      if (result === \"granted\")\n        permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  };\n  const { on: onClick, trigger: clickTrigger } = createEventHook();\n  const { on: onShow, trigger: showTrigger } = createEventHook();\n  const { on: onError, trigger: errorTrigger } = createEventHook();\n  const { on: onClose, trigger: closeTrigger } = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value || !permissionGranted.value)\n      return;\n    const options2 = Object.assign({}, defaultWebNotificationOptions, overrides);\n    notification.value = new Notification(options2.title || \"\", options2);\n    notification.value.onclick = clickTrigger;\n    notification.value.onshow = showTrigger;\n    notification.value.onerror = errorTrigger;\n    notification.value.onclose = closeTrigger;\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  if (_requestForPermissions)\n    tryOnMounted(ensurePermissions);\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    ensurePermissions,\n    permissionGranted,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n}\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = ref(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = toRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let pongTimeoutWait;\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const close = (code = 1e3, reason) => {\n    if (!isClient || !wsRef.value)\n      return;\n    explicitlyClosed = true;\n    resetHeartbeat();\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      wsRef.value = void 0;\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === message)\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(\n      () => {\n        send(message, false);\n        if (pongTimeoutWait != null)\n          return;\n        pongTimeoutWait = setTimeout(() => {\n          close();\n          explicitlyClosed = false;\n        }, pongTimeout);\n      },\n      interval,\n      { immediate: false }\n    );\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    if (isClient)\n      useEventListener(\"beforeunload\", () => close());\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    if (!isClient && !isWorker)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    watch(urlRef, open, { immediate: true });\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = (...args) => {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(...args);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (typeof arg0 === \"string\")\n      worker.value = new Worker(arg0, workerOptions);\n    else if (typeof arg0 === \"function\")\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nfunction jobRunner(userFunc) {\n  return (e) => {\n    const userFuncArgs = e.data[0];\n    return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n      postMessage([\"SUCCESS\", result]);\n    }).catch((error) => {\n      postMessage([\"ERROR\", error]);\n    });\n  };\n}\n\nfunction depsParser(deps) {\n  if (deps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  return `importScripts(${depsString})`;\n}\n\nfunction createWorkerBlobUrl(fn, deps) {\n  const blobCode = `${depsParser(deps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n}\n\nfunction useWebWorkerFn(fn, options = {}) {\n  const {\n    dependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = ref(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = ref();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      e.preventDefault();\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(\n        () => workerTerminate(\"TIMEOUT_EXPIRED\"),\n        timeout\n      );\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    promise.value = {\n      resolve,\n      reject\n    };\n    worker.value && worker.value.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\n        \"[useWebWorkerFn] You can only run one instance of the worker at a time.\"\n      );\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n}\n\nfunction useWindowFocus(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref(false);\n  const focused = ref(window.document.hasFocus());\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  });\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  });\n  return focused;\n}\n\nfunction useWindowScroll(options = {}) {\n  const { window = defaultWindow, behavior = \"auto\" } = options;\n  if (!window) {\n    return {\n      x: ref(0),\n      y: ref(0)\n    };\n  }\n  const internalX = ref(window.scrollX);\n  const internalY = ref(window.scrollY);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo({ left: x2, behavior });\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo({ top: y2, behavior });\n    }\n  });\n  useEventListener(\n    window,\n    \"scroll\",\n    () => {\n      internalX.value = window.scrollX;\n      internalY.value = window.scrollY;\n    },\n    {\n      capture: false,\n      passive: true\n    }\n  );\n  return { x, y };\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Number.POSITIVE_INFINITY,\n    initialHeight = Number.POSITIVE_INFINITY,\n    listenOrientation = true,\n    includeScrollbar = true\n  } = options;\n  const width = ref(initialWidth);\n  const height = ref(initialHeight);\n  const update = () => {\n    if (window) {\n      if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  useEventListener(\"resize\", update, { passive: true });\n  if (listenOrientation) {\n    const matches = useMediaQuery(\"(orientation: portrait)\");\n    watch(matches, () => update());\n  }\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsMasterCss, breakpointsPrimeFlex, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, cloneFnJSON, computedAsync, computedInject, createFetch, createReusableTemplate, createTemplatePromise, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, executeTransition, formatTimeAgo, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, setSSRHandler, templateRef, unrefElement, useActiveElement, useAnimate, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useClipboardItems, useCloned, useColorMode, useConfirmDialog, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, useParentElement, usePerformanceObserver, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePrevious, useRafFn, useRefHistory, useResizeObserver, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n", "import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined || !isFocusable(node, config.tabbableOptions)) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      const onPause = getOption(pauseOptions, 'onPause');\n      const onPostPause = getOption(pauseOptions, 'onPostPause');\n\n      state.paused = true;\n      onPause?.();\n\n      removeListeners();\n      updateObservedNodes();\n\n      onPostPause?.();\n      return this;\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      const onUnpause = getOption(unpauseOptions, 'onUnpause');\n      const onPostUnpause = getOption(unpauseOptions, 'onPostUnpause');\n\n      state.paused = false;\n      onUnpause?.();\n\n      updateTabbableNodes();\n      addListeners();\n      updateObservedNodes();\n\n      onPostUnpause?.();\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n", "import { unrefElement, tryOnScopeDispose } from '@vueuse/core';\nimport { ref, watch } from 'vue-demi';\nimport { createFocusTrap } from 'focus-trap';\n\nfunction useFocusTrap(target, options = {}) {\n  let trap;\n  const { immediate, ...focusTrapOptions } = options;\n  const hasFocus = ref(false);\n  const isPaused = ref(false);\n  const activate = (opts) => trap && trap.activate(opts);\n  const deactivate = (opts) => trap && trap.deactivate(opts);\n  const pause = () => {\n    if (trap) {\n      trap.pause();\n      isPaused.value = true;\n    }\n  };\n  const unpause = () => {\n    if (trap) {\n      trap.unpause();\n      isPaused.value = false;\n    }\n  };\n  watch(\n    () => unrefElement(target),\n    (el) => {\n      if (!el)\n        return;\n      trap = createFocusTrap(el, {\n        ...focusTrapOptions,\n        onActivate() {\n          hasFocus.value = true;\n          if (options.onActivate)\n            options.onActivate();\n        },\n        onDeactivate() {\n          hasFocus.value = false;\n          if (options.onDeactivate)\n            options.onDeactivate();\n        }\n      });\n      if (immediate)\n        activate();\n    },\n    { flush: \"post\" }\n  );\n  tryOnScopeDispose(() => deactivate());\n  return {\n    hasFocus,\n    isPaused,\n    activate,\n    deactivate,\n    pause,\n    unpause\n  };\n}\n\nexport { useFocusTrap };\n"], "mappings": ";;;;;;;;;AAgDA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AA0JA,SAAS,QAAQ,GAAG;AAClB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AA4EA,IAAM,WAAW,OAAO,WAAW,eAAe,OAAO,aAAa;AACtE,IAAM,WAAW,OAAO,sBAAsB,eAAe,sBAAsB;AAoBnF,IAAM,QAAwB,SAAS;AACvC,SAAS,WAAW;AAClB,MAAI,IAAI;AACR,SAAO,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,eAAe,iBAAiB,KAAK,OAAO,UAAU,SAAS,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,KAAK,UAAU,OAAO,SAAS,OAAO,UAAU,SAAS;AAC5U;AAyHA,SAAS,oBAAoB,IAAI;AAC/B,QAAM,QAAwB,uBAAO,OAAO,IAAI;AAChD,SAAO,CAAC,QAAQ;AACd,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AACA,IAAM,cAAc;AACpB,IAAM,YAAY,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,aAAa,KAAK,EAAE,YAAY,CAAC;AAC5F,IAAM,aAAa;AACnB,IAAM,WAAW,oBAAoB,CAAC,QAAQ;AAC5C,SAAO,IAAI,QAAQ,YAAY,CAAC,GAAG,MAAM,IAAI,EAAE,YAAY,IAAI,EAAE;AACnE,CAAC;AAUD,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;;;ACrRA,SAAS,aAAa,OAAO;AAC3B,MAAI;AACJ,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQ,KAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,KAAK;AAClE;AAGA,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AA+yCrD,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,IAAM,WAA2B,YAAY;AAC7C,SAAS,cAAc;AACrB,MAAI,EAAE,aAAa;AACjB,YAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC9C,SAAO,QAAQ,SAAS;AAC1B;AAuzGA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AAmlCrD,IAAM,gBAAgB;AAAA,EACpB,EAAE,KAAK,KAAK,OAAO,KAAK,MAAM,SAAS;AAAA,EACvC,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,SAAS;AAAA,EACzC,EAAE,KAAK,MAAM,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC,EAAE,KAAK,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,EACzC,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AAAA,EAC5C,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAAA,EAC7C,EAAE,KAAK,OAAO,mBAAmB,OAAO,SAAS,MAAM,OAAO;AAChE;AAgLA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoC,OAAO,OAAO,CAAC,GAAG,EAAE,QAAQ,SAAS,GAAG,kBAAkB;;;ACrlMpG,IAAMA,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAUzC,IAAMC,UAAU,SAAVA,SAAoBC,MAAMC,QAAe;AAAA,MAAAC;AAAA,MAAfD,WAAM,QAAA;AAANA,aAAS;EAAI;AAI3C,MAAME,WAAWH,SAAI,QAAJA,SAAIE,SAAAA,UAAAA,qBAAJF,KAAMI,kBAAYF,QAAAA,uBAAA,SAAA,SAAlBA,mBAAAL,KAAAG,MAAqB,OAAO;AAC7C,MAAMK,QAAQF,aAAa,MAAMA,aAAa;AAO9C,MAAMG,SAASD,SAAUJ,UAAUD,QAAQD,SAAQC,KAAKO,UAAU;AAElE,SAAOD;AACT;AAOA,IAAME,oBAAoB,SAApBA,mBAA8BR,MAAM;AAAA,MAAAS;AAIxC,MAAMC,WAAWV,SAAI,QAAJA,SAAIS,SAAAA,UAAAA,sBAAJT,KAAMI,kBAAYK,QAAAA,wBAAA,SAAA,SAAlBA,oBAAAZ,KAAAG,MAAqB,iBAAiB;AACvD,SAAOU,aAAa,MAAMA,aAAa;AACzC;AAQA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAIC,kBAAkBC,QAAQ;AAG5D,MAAIf,QAAQa,EAAE,GAAG;AACf,WAAO,CAAA;EACT;AAEA,MAAIG,aAAaC,MAAMzB,UAAU0B,MAAMC,MACrCN,GAAGO,iBAAiBjC,iBAAiB,CACvC;AACA,MAAI2B,oBAAoBvB,QAAQO,KAAKe,IAAI1B,iBAAiB,GAAG;AAC3D6B,eAAWK,QAAQR,EAAE;EACvB;AACAG,eAAaA,WAAWD,OAAOA,MAAM;AACrC,SAAOC;AACT;AAoCA,IAAMM,2BAA2B,SAA3BA,0BACJC,UACAT,kBACAU,SACA;AACA,MAAMR,aAAa,CAAA;AACnB,MAAMS,kBAAkBR,MAAMS,KAAKH,QAAQ;AAC3C,SAAOE,gBAAgBE,QAAQ;AAC7B,QAAM/B,UAAU6B,gBAAgBG,MAAK;AACrC,QAAI5B,QAAQJ,SAAS,KAAK,GAAG;AAG3B;IACF;AAEA,QAAIA,QAAQiC,YAAY,QAAQ;AAE9B,UAAMC,WAAWlC,QAAQmC,iBAAgB;AACzC,UAAMC,UAAUF,SAASH,SAASG,WAAWlC,QAAQqC;AACrD,UAAMC,mBAAmBZ,0BAAyBU,SAAS,MAAMR,OAAO;AACxE,UAAIA,QAAQW,SAAS;AACnBnB,mBAAWoB,KAAIjB,MAAfH,YAAmBkB,gBAAgB;MACrC,OAAO;AACLlB,mBAAWoB,KAAK;UACdC,aAAazC;UACboB,YAAYkB;QACd,CAAC;MACH;IACF,OAAO;AAEL,UAAMI,iBAAiB/C,QAAQO,KAAKF,SAAST,iBAAiB;AAC9D,UACEmD,kBACAd,QAAQT,OAAOnB,OAAO,MACrBkB,oBAAoB,CAACS,SAASgB,SAAS3C,OAAO,IAC/C;AACAoB,mBAAWoB,KAAKxC,OAAO;MACzB;AAGA,UAAM4C,aACJ5C,QAAQ4C;MAEP,OAAOhB,QAAQiB,kBAAkB,cAChCjB,QAAQiB,cAAc7C,OAAO;AAKjC,UAAM8C,kBACJ,CAAC1C,QAAQwC,YAAY,KAAK,MACzB,CAAChB,QAAQmB,oBAAoBnB,QAAQmB,iBAAiB/C,OAAO;AAEhE,UAAI4C,cAAcE,iBAAiB;AAOjC,YAAMR,oBAAmBZ,0BACvBkB,eAAe,OAAO5C,QAAQqC,WAAWO,WAAWP,UACpD,MACAT,OACF;AAEA,YAAIA,QAAQW,SAAS;AACnBnB,qBAAWoB,KAAIjB,MAAfH,YAAmBkB,iBAAgB;QACrC,OAAO;AACLlB,qBAAWoB,KAAK;YACdC,aAAazC;YACboB,YAAYkB;UACd,CAAC;QACH;MACF,OAAO;AAGLT,wBAAgBJ,QAAOF,MAAvBM,iBAA2B7B,QAAQqC,QAAQ;MAC7C;IACF;EACF;AACA,SAAOjB;AACT;AAQA,IAAM4B,cAAc,SAAdA,aAAwB3C,MAAM;AAClC,SAAO,CAAC4C,MAAMC,SAAS7C,KAAKI,aAAa,UAAU,GAAG,EAAE,CAAC;AAC3D;AAQA,IAAM0C,cAAc,SAAdA,aAAwB9C,MAAM;AAClC,MAAI,CAACA,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AAEA,MAAI/C,KAAKgD,WAAW,GAAG;AAQrB,SACG,0BAA0BC,KAAKjD,KAAK4B,OAAO,KAC1CpB,kBAAkBR,IAAI,MACxB,CAAC2C,YAAY3C,IAAI,GACjB;AACA,aAAO;IACT;EACF;AAEA,SAAOA,KAAKgD;AACd;AAUA,IAAME,uBAAuB,SAAvBA,sBAAiClD,MAAMmD,SAAS;AACpD,MAAMH,WAAWF,YAAY9C,IAAI;AAEjC,MAAIgD,WAAW,KAAKG,WAAW,CAACR,YAAY3C,IAAI,GAAG;AACjD,WAAO;EACT;AAEA,SAAOgD;AACT;AAEA,IAAMI,uBAAuB,SAAvBA,sBAAiCC,GAAGC,GAAG;AAC3C,SAAOD,EAAEL,aAAaM,EAAEN,WACpBK,EAAEE,gBAAgBD,EAAEC,gBACpBF,EAAEL,WAAWM,EAAEN;AACrB;AAEA,IAAMQ,UAAU,SAAVA,SAAoBxD,MAAM;AAC9B,SAAOA,KAAK4B,YAAY;AAC1B;AAEA,IAAM6B,gBAAgB,SAAhBA,eAA0BzD,MAAM;AACpC,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMC,uBAAuB,SAAvBA,sBAAiC3D,MAAM;AAC3C,MAAM4D,IACJ5D,KAAK4B,YAAY,aACjBZ,MAAMzB,UAAU0B,MACbC,MAAMlB,KAAKgC,QAAQ,EACnB6B,KAAK,SAACC,OAAK;AAAA,WAAKA,MAAMlC,YAAY;GAAU;AACjD,SAAOgC;AACT;AAEA,IAAMG,kBAAkB,SAAlBA,iBAA4BC,OAAOC,MAAM;AAC7C,WAASC,IAAI,GAAGA,IAAIF,MAAMtC,QAAQwC,KAAK;AACrC,QAAIF,MAAME,CAAC,EAAEC,WAAWH,MAAME,CAAC,EAAED,SAASA,MAAM;AAC9C,aAAOD,MAAME,CAAC;IAChB;EACF;AACF;AAEA,IAAME,kBAAkB,SAAlBA,iBAA4BpE,MAAM;AACtC,MAAI,CAACA,KAAKqE,MAAM;AACd,WAAO;EACT;AACA,MAAMC,aAAatE,KAAKiE,QAAQvE,YAAYM,IAAI;AAChD,MAAMuE,cAAc,SAAdA,aAAwBF,MAAM;AAClC,WAAOC,WAAWnD,iBAChB,+BAA+BkD,OAAO,IACxC;;AAGF,MAAIG;AACJ,MACE,OAAOC,WAAW,eAClB,OAAOA,OAAOC,QAAQ,eACtB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAH,eAAWD,YAAYE,OAAOC,IAAIC,OAAO3E,KAAKqE,IAAI,CAAC;EACrD,OAAO;AACL,QAAI;AACFG,iBAAWD,YAAYvE,KAAKqE,IAAI;aACzBO,KAAK;AAEZC,cAAQC,MACN,4IACAF,IAAIG,OACN;AACA,aAAO;IACT;EACF;AAEA,MAAMZ,UAAUJ,gBAAgBS,UAAUxE,KAAKiE,IAAI;AACnD,SAAO,CAACE,WAAWA,YAAYnE;AACjC;AAEA,IAAMgF,UAAU,SAAVA,SAAoBhF,MAAM;AAC9B,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMuB,qBAAqB,SAArBA,oBAA+BjF,MAAM;AACzC,SAAOgF,QAAQhF,IAAI,KAAK,CAACoE,gBAAgBpE,IAAI;AAC/C;AAGA,IAAMkF,iBAAiB,SAAjBA,gBAA2BlF,MAAM;AAAA,MAAAmF;AAwBrC,MAAIC,WAAWpF,QAAQN,YAAYM,IAAI;AACvC,MAAIqF,gBAAYF,YAAGC,cAAQ,QAAAD,cAAA,SAAA,SAARA,UAAUG;AAI7B,MAAIC,WAAW;AACf,MAAIH,YAAYA,aAAapF,MAAM;AAAA,QAAAwF,eAAAC,uBAAAC;AACjCH,eAAW,CAAC,GACVC,gBAAAH,kBAAYG,QAAAA,kBAAA,WAAAC,wBAAZD,cAAc1F,mBAAa,QAAA2F,0BAAA,UAA3BA,sBAA6BE,SAASN,YAAY,KAClDrF,SAAI,QAAJA,SAAI0F,WAAAA,sBAAJ1F,KAAMF,mBAAa4F,QAAAA,wBAAA,UAAnBA,oBAAqBC,SAAS3F,IAAI;AAGpC,WAAO,CAACuF,YAAYF,cAAc;AAAA,UAAAO,YAAAC,gBAAAC;AAIhCV,iBAAW1F,YAAY2F,YAAY;AACnCA,sBAAYO,aAAGR,cAAQ,QAAAQ,eAAA,SAAA,SAARA,WAAUN;AACzBC,iBAAW,CAAC,GAAAM,iBAACR,kBAAY,QAAAQ,mBAAA,WAAAC,wBAAZD,eAAc/F,mBAAa,QAAAgG,0BAAA,UAA3BA,sBAA6BH,SAASN,YAAY;IACjE;EACF;AAEA,SAAOE;AACT;AAEA,IAAMQ,aAAa,SAAbA,YAAuB/F,MAAM;AACjC,MAAAgG,wBAA0BhG,KAAKiG,sBAAqB,GAA5CC,QAAKF,sBAALE,OAAOC,SAAMH,sBAANG;AACf,SAAOD,UAAU,KAAKC,WAAW;AACnC;AACA,IAAMC,WAAW,SAAXA,UAAqBpG,MAAIqG,MAAmC;AAAA,MAA/BC,eAAYD,KAAZC,cAAc9D,gBAAa6D,KAAb7D;AAM/C,MAAI+D,iBAAiBvG,IAAI,EAAEwG,eAAe,UAAU;AAClD,WAAO;EACT;AAEA,MAAMC,kBAAkBnH,QAAQO,KAAKG,MAAM,+BAA+B;AAC1E,MAAM0G,mBAAmBD,kBAAkBzG,KAAK2G,gBAAgB3G;AAChE,MAAIV,QAAQO,KAAK6G,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;EACT;AAEA,MACE,CAACJ,gBACDA,iBAAiB,UACjBA,iBAAiB,eACjB;AACA,QAAI,OAAO9D,kBAAkB,YAAY;AAGvC,UAAMoE,eAAe5G;AACrB,aAAOA,MAAM;AACX,YAAM2G,gBAAgB3G,KAAK2G;AAC3B,YAAME,WAAWnH,YAAYM,IAAI;AACjC,YACE2G,iBACA,CAACA,cAAcpE,cACfC,cAAcmE,aAAa,MAAM,MACjC;AAGA,iBAAOZ,WAAW/F,IAAI;QACxB,WAAWA,KAAK8G,cAAc;AAE5B9G,iBAAOA,KAAK8G;mBACH,CAACH,iBAAiBE,aAAa7G,KAAKF,eAAe;AAE5DE,iBAAO6G,SAASvB;QAClB,OAAO;AAELtF,iBAAO2G;QACT;MACF;AAEA3G,aAAO4G;IACT;AAWA,QAAI1B,eAAelF,IAAI,GAAG;AAKxB,aAAO,CAACA,KAAK+G,eAAc,EAAGrF;IAChC;AAkBA,QAAI4E,iBAAiB,eAAe;AAClC,aAAO;IACT;EAEF,WAAWA,iBAAiB,iBAAiB;AAM3C,WAAOP,WAAW/F,IAAI;EACxB;AAIA,SAAO;AACT;AAKA,IAAMgH,yBAAyB,SAAzBA,wBAAmChH,MAAM;AAC7C,MAAI,mCAAmCiD,KAAKjD,KAAK4B,OAAO,GAAG;AACzD,QAAIrB,aAAaP,KAAK2G;AAEtB,WAAOpG,YAAY;AACjB,UAAIA,WAAWqB,YAAY,cAAcrB,WAAW0G,UAAU;AAE5D,iBAAS/C,IAAI,GAAGA,IAAI3D,WAAWyB,SAASN,QAAQwC,KAAK;AACnD,cAAMJ,QAAQvD,WAAWyB,SAASkF,KAAKhD,CAAC;AAExC,cAAIJ,MAAMlC,YAAY,UAAU;AAG9B,mBAAOtC,QAAQO,KAAKU,YAAY,sBAAsB,IAClD,OACA,CAACuD,MAAM6B,SAAS3F,IAAI;UAC1B;QACF;AAEA,eAAO;MACT;AACAO,mBAAaA,WAAWoG;IAC1B;EACF;AAIA,SAAO;AACT;AAEA,IAAMQ,kCAAkC,SAAlCA,iCAA4C5F,SAASvB,MAAM;AAC/D,MACEA,KAAKiH;;;EAILlH,QAAQC,IAAI,KACZyD,cAAczD,IAAI,KAClBoG,SAASpG,MAAMuB,OAAO;EAEtBoC,qBAAqB3D,IAAI,KACzBgH,uBAAuBhH,IAAI,GAC3B;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMoH,iCAAiC,SAAjCA,gCAA2C7F,SAASvB,MAAM;AAC9D,MACEiF,mBAAmBjF,IAAI,KACvB8C,YAAY9C,IAAI,IAAI,KACpB,CAACmH,gCAAgC5F,SAASvB,IAAI,GAC9C;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMqH,4BAA4B,SAA5BA,2BAAsCC,gBAAgB;AAC1D,MAAMtE,WAAWH,SAASyE,eAAelH,aAAa,UAAU,GAAG,EAAE;AACrE,MAAIwC,MAAMI,QAAQ,KAAKA,YAAY,GAAG;AACpC,WAAO;EACT;AAGA,SAAO;AACT;AAMA,IAAMuE,cAAc,SAAdA,aAAwBxG,YAAY;AACxC,MAAMyG,mBAAmB,CAAA;AACzB,MAAMC,mBAAmB,CAAA;AACzB1G,aAAW2G,QAAQ,SAAUR,MAAMhD,GAAG;AACpC,QAAMf,UAAU,CAAC,CAAC+D,KAAK9E;AACvB,QAAMzC,UAAUwD,UAAU+D,KAAK9E,cAAc8E;AAC7C,QAAMS,oBAAoBzE,qBAAqBvD,SAASwD,OAAO;AAC/D,QAAM7B,WAAW6B,UAAUoE,aAAYL,KAAKnG,UAAU,IAAIpB;AAC1D,QAAIgI,sBAAsB,GAAG;AAC3BxE,gBACIqE,iBAAiBrF,KAAIjB,MAArBsG,kBAAyBlG,QAAQ,IACjCkG,iBAAiBrF,KAAKxC,OAAO;IACnC,OAAO;AACL8H,uBAAiBtF,KAAK;QACpBoB,eAAeW;QACflB,UAAU2E;QACVT;QACA/D;QACApB,SAAST;MACX,CAAC;IACH;EACF,CAAC;AAED,SAAOmG,iBACJG,KAAKxE,oBAAoB,EACzByE,OAAO,SAACC,KAAKC,UAAa;AACzBA,aAAS5E,UACL2E,IAAI3F,KAAIjB,MAAR4G,KAAYC,SAAShG,OAAO,IAC5B+F,IAAI3F,KAAK4F,SAAShG,OAAO;AAC7B,WAAO+F;EACT,GAAG,CAAA,CAAE,EACJE,OAAOR,gBAAgB;AAC5B;AAEMS,IAAAA,WAAW,SAAXA,UAAqBC,WAAW3G,SAAS;AAC7CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQsG,+BAA+Be,KAAK,MAAM5G,OAAO;MACzDW,SAAS;MACTM,eAAejB,QAAQiB;MACvBE,kBAAkB2E;IACpB,CACF;EACF,OAAO;AACLtG,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRuG,+BAA+Be,KAAK,MAAM5G,OAAO,CACnD;EACF;AACA,SAAOgG,YAAYxG,UAAU;AAC/B;AAEMqH,IAAAA,YAAY,SAAZA,WAAsBF,WAAW3G,SAAS;AAC9CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQqG,gCAAgCgB,KAAK,MAAM5G,OAAO;MAC1DW,SAAS;MACTM,eAAejB,QAAQiB;IACzB,CACF;EACF,OAAO;AACLzB,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRsG,gCAAgCgB,KAAK,MAAM5G,OAAO,CACpD;EACF;AAEA,SAAOR;AACT;AAEMsH,IAAAA,aAAa,SAAbA,YAAuBrI,MAAMuB,SAAS;AAC1CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMd,iBAAiB,MAAM,OAAO;AACnD,WAAO;EACT;AACA,SAAOkI,+BAA+B7F,SAASvB,IAAI;AACrD;AAEA,IAAMsI,6BAA6CrJ,mBAChD+I,OAAO,QAAQ,EACf7I,KAAK,GAAG;AAELoJ,IAAAA,cAAc,SAAdA,aAAwBvI,MAAMuB,SAAS;AAC3CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMsI,0BAA0B,MAAM,OAAO;AAC5D,WAAO;EACT;AACA,SAAOnB,gCAAgC5F,SAASvB,IAAI;AACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrqBA,IAAMwI,mBAAmB;EACvBC,cAAYA,SAAAA,aAACC,WAAWC,MAAM;AAC5B,QAAID,UAAUE,SAAS,GAAG;AACxB,UAAMC,aAAaH,UAAUA,UAAUE,SAAS,CAAC;AACjD,UAAIC,eAAeF,MAAM;AACvBE,mBAAWC,MAAK;MAClB;IACF;AAEA,QAAMC,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUO,KAAKN,IAAI;IACrB,OAAO;AAELD,gBAAUQ,OAAOH,WAAW,CAAC;AAC7BL,gBAAUO,KAAKN,IAAI;IACrB;;EAGFQ,gBAAcA,SAAAA,eAACT,WAAWC,MAAM;AAC9B,QAAMI,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUQ,OAAOH,WAAW,CAAC;IAC/B;AAEA,QAAIL,UAAUE,SAAS,GAAG;AACxBF,gBAAUA,UAAUE,SAAS,CAAC,EAAEQ,QAAO;IACzC;EACF;AACF;AAEA,IAAMC,oBAAoB,SAApBA,mBAA8BC,MAAM;AACxC,SACEA,KAAKC,WACLD,KAAKC,QAAQC,YAAW,MAAO,WAC/B,OAAOF,KAAKG,WAAW;AAE3B;AAEA,IAAMC,gBAAgB,SAAhBA,eAA0BC,GAAG;AACjC,UAAOA,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGC,SAAQ,aAAYD,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGC,SAAQ,UAASD,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGE,aAAY;AACnE;AAEA,IAAMC,aAAa,SAAbA,YAAuBH,GAAG;AAC9B,UAAOA,MAAAA,QAAAA,MAAC,SAAA,SAADA,EAAGC,SAAQ,UAASD,MAAC,QAADA,MAAC,SAAA,SAADA,EAAGE,aAAY;AAC5C;AAGA,IAAME,eAAe,SAAfA,cAAyBJ,GAAG;AAChC,SAAOG,WAAWH,CAAC,KAAK,CAACA,EAAEK;AAC7B;AAGA,IAAMC,gBAAgB,SAAhBA,eAA0BN,GAAG;AACjC,SAAOG,WAAWH,CAAC,KAAKA,EAAEK;AAC5B;AAEA,IAAME,QAAQ,SAARA,OAAkBC,IAAI;AAC1B,SAAOC,WAAWD,IAAI,CAAC;AACzB;AAIA,IAAME,YAAY,SAAZA,WAAsBC,KAAKH,IAAI;AACnC,MAAII,MAAM;AAEVD,MAAIE,MAAM,SAAUC,OAAOC,GAAG;AAC5B,QAAIP,GAAGM,KAAK,GAAG;AACbF,YAAMG;AACN,aAAO;IACT;AAEA,WAAO;EACT,CAAC;AAED,SAAOH;AACT;AASA,IAAMI,iBAAiB,SAAjBA,gBAA2BF,OAAkB;AAAA,WAAAG,OAAAC,UAAAjC,QAARkC,SAAM,IAAAC,MAAAH,OAAAA,IAAAA,OAAA,IAAA,CAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAANF,WAAME,OAAAH,CAAAA,IAAAA,UAAAG,IAAA;EAAA;AAC/C,SAAO,OAAOP,UAAU,aAAaA,MAAKQ,MAAIH,QAAAA,MAAM,IAAIL;AAC1D;AAEA,IAAMS,kBAAkB,SAAlBA,iBAA4BC,OAAO;AAQvC,SAAOA,MAAMC,OAAOC,cAAc,OAAOF,MAAMG,iBAAiB,aAC5DH,MAAMG,aAAY,EAAG,CAAC,IACtBH,MAAMC;AACZ;AAIA,IAAMG,oBAAoB,CAAA;AAEpBC,IAAAA,kBAAkB,SAAlBA,iBAA4BC,UAAUC,aAAa;AAGvD,MAAMC,OAAMD,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAaE,aAAYA;AAErC,MAAMlD,aAAYgD,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAahD,cAAa6C;AAE5C,MAAMM,SAAMC,eAAA;IACVC,yBAAyB;IACzBC,mBAAmB;IACnBC,mBAAmB;IACnBlC;IACAE;EAAa,GACVyB,WAAW;AAGhB,MAAMQ,QAAQ;;;IAGZC,YAAY,CAAA;;;;;;;;;;;;;;;;;IAkBZC,iBAAiB,CAAA;;;;;;IAMjBC,gBAAgB,CAAA;IAEhBC,6BAA6B;IAC7BC,yBAAyB;IACzBC,QAAQ;IACRC,QAAQ;;;IAIRC,wBAAwBC;;IAGxBC,gBAAgBD;;AAGlB,MAAIhE;AAUJ,MAAMkE,YAAY,SAAZA,WAAaC,uBAAuBC,YAAYC,kBAAqB;AACzE,WAAOF,yBACLA,sBAAsBC,UAAU,MAAMJ,SACpCG,sBAAsBC,UAAU,IAChClB,OAAOmB,oBAAoBD,UAAU;;AAa3C,MAAME,qBAAqB,SAArBA,oBAA+BC,SAAS/B,OAAO;AACnD,QAAMG,eACJ,QAAOH,UAAAA,QAAAA,UAAK,SAAA,SAALA,MAAOG,kBAAiB,aAC3BH,MAAMG,aAAY,IAClBqB;AAIN,WAAOT,MAAME,gBAAgB/B,UAC3B,SAAA8C,MAAA;AAAA,UAAGC,YAASD,KAATC,WAAWC,gBAAaF,KAAbE;AAAa,aACzBD,UAAUE,SAASJ,OAAO;;;;OAK1B5B,iBAAAA,QAAAA,iBAAAA,SAAAA,SAAAA,aAAciC,SAASH,SAAS,MAChCC,cAAcG,KAAK,SAAClE,MAAI;AAAA,eAAKA,SAAS4D;OAAQ;IAAA,CAClD;;AAgBF,MAAMO,mBAAmB,SAAnBA,kBAA6BV,YAAuB;AACxD,QAAIW,cAAc7B,OAAOkB,UAAU;AAEnC,QAAI,OAAOW,gBAAgB,YAAY;AAAA,eAAAC,QAAA9C,UAAAjC,QAHSkC,SAAM,IAAAC,MAAA4C,QAAAA,IAAAA,QAAA,IAAA,CAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAN9C,eAAM8C,QAAA/C,CAAAA,IAAAA,UAAA+C,KAAA;MAAA;AAIpDF,oBAAcA,YAAWzC,MAAA,QAAIH,MAAM;IACrC;AAEA,QAAI4C,gBAAgB,MAAM;AACxBA,oBAAcf;IAChB;AAEA,QAAI,CAACe,aAAa;AAChB,UAAIA,gBAAgBf,UAAae,gBAAgB,OAAO;AACtD,eAAOA;MACT;AAGA,YAAM,IAAIG,MAAK,IAAAC,OACRf,YAAU,8DAAA,CACjB;IACF;AAEA,QAAIzD,OAAOoE;AAEX,QAAI,OAAOA,gBAAgB,UAAU;AACnCpE,aAAOqC,IAAIoC,cAAcL,WAAW;AACpC,UAAI,CAACpE,MAAM;AACT,cAAM,IAAIuE,MAAK,IAAAC,OACRf,YAAU,uCAAA,CACjB;MACF;IACF;AAEA,WAAOzD;;AAGT,MAAM0E,sBAAsB,SAAtBA,uBAAkC;AACtC,QAAI1E,OAAOmE,iBAAiB,cAAc;AAG1C,QAAInE,SAAS,OAAO;AAClB,aAAO;IACT;AAEA,QAAIA,SAASqD,UAAa,CAACsB,YAAY3E,MAAMuC,OAAOqC,eAAe,GAAG;AAEpE,UAAIjB,mBAAmBtB,IAAIwC,aAAa,KAAK,GAAG;AAC9C7E,eAAOqC,IAAIwC;MACb,OAAO;AACL,YAAMC,qBAAqBlC,MAAMG,eAAe,CAAC;AACjD,YAAMgC,oBACJD,sBAAsBA,mBAAmBC;AAG3C/E,eAAO+E,qBAAqBZ,iBAAiB,eAAe;MAC9D;IACF;AAEA,QAAI,CAACnE,MAAM;AACT,YAAM,IAAIuE,MACR,8DACF;IACF;AAEA,WAAOvE;;AAGT,MAAMgF,sBAAsB,SAAtBA,uBAAkC;AACtCpC,UAAME,kBAAkBF,MAAMC,WAAWoC,IAAI,SAACnB,WAAc;AAC1D,UAAMC,gBAAgBmB,SAASpB,WAAWvB,OAAOqC,eAAe;AAKhE,UAAMO,iBAAiBC,UAAUtB,WAAWvB,OAAOqC,eAAe;AAElE,UAAMG,oBACJhB,cAAczE,SAAS,IAAIyE,cAAc,CAAC,IAAIV;AAChD,UAAMgC,mBACJtB,cAAczE,SAAS,IACnByE,cAAcA,cAAczE,SAAS,CAAC,IACtC+D;AAEN,UAAMiC,uBAAuBH,eAAejB,KAAK,SAAClE,MAAI;AAAA,eACpDuF,WAAWvF,IAAI;MAAC,CAClB;AACA,UAAMwF,sBAAsBL,eACzBM,MAAK,EACLC,QAAO,EACPxB,KAAK,SAAClE,MAAI;AAAA,eAAKuF,WAAWvF,IAAI;OAAE;AAEnC,UAAM2F,qBAAqB,CAAC,CAAC5B,cAAcG,KACzC,SAAClE,MAAI;AAAA,eAAK4F,YAAY5F,IAAI,IAAI;MAAC,CACjC;AAEA,aAAO;QACL8D;QACAC;QACAoB;;QAGAQ;;QAGAZ;;QAEAM;;;;;;;;;QAUAC;;QAEAE;;;;;;;;;QAUAK,kBAAgB,SAAAA,iBAAC7F,MAAsB;AAAA,cAAhB8F,UAAOvE,UAAAjC,SAAA,KAAAiC,UAAA,CAAA,MAAA8B,SAAA9B,UAAA,CAAA,IAAG;AAC/B,cAAMwE,UAAUhC,cAAcrE,QAAQM,IAAI;AAC1C,cAAI+F,UAAU,GAAG;AAOf,gBAAID,SAAS;AACX,qBAAOX,eACJM,MAAMN,eAAezF,QAAQM,IAAI,IAAI,CAAC,EACtCkE,KAAK,SAAC8B,IAAE;AAAA,uBAAKT,WAAWS,EAAE;eAAE;YACjC;AAEA,mBAAOb,eACJM,MAAM,GAAGN,eAAezF,QAAQM,IAAI,CAAC,EACrC0F,QAAO,EACPxB,KAAK,SAAC8B,IAAE;AAAA,qBAAKT,WAAWS,EAAE;aAAE;UACjC;AAEA,iBAAOjC,cAAcgC,WAAWD,UAAU,IAAI,GAAG;QACnD;;IAEJ,CAAC;AAEDlD,UAAMG,iBAAiBH,MAAME,gBAAgBmD,OAC3C,SAACC,OAAK;AAAA,aAAKA,MAAMnC,cAAczE,SAAS;IAAC,CAC3C;AAGA,QACEsD,MAAMG,eAAezD,UAAU,KAC/B,CAAC6E,iBAAiB,eAAe,GACjC;AACA,YAAM,IAAII,MACR,qGACF;IACF;AASA,QACE3B,MAAME,gBAAgBoB,KAAK,SAACiC,GAAC;AAAA,aAAKA,EAAER;KAAmB,KACvD/C,MAAME,gBAAgBxD,SAAS,GAC/B;AACA,YAAM,IAAIiF,MACR,+KACF;IACF;;AAWF,MAAM6B,mBAAmB,SAAnBA,kBAA6BJ,IAAI;AACrC,QAAMnB,gBAAgBmB,GAAGnB;AAEzB,QAAI,CAACA,eAAe;AAClB;IACF;AAEA,QACEA,cAAc9C,cACd8C,cAAc9C,WAAW8C,kBAAkB,MAC3C;AACA,aAAOuB,kBAAiBvB,cAAc9C,UAAU;IAClD;AAEA,WAAO8C;;AAGT,MAAMwB,WAAW,SAAXA,UAAqBrG,MAAM;AAC/B,QAAIA,SAAS,OAAO;AAClB;IACF;AAEA,QAAIA,SAASoG,iBAAiB9D,QAAQ,GAAG;AACvC;IACF;AAEA,QAAI,CAACtC,QAAQ,CAACA,KAAKsG,OAAO;AACxBD,MAAAA,UAAS3B,oBAAmB,CAAE;AAC9B;IACF;AAEA1E,SAAKsG,MAAM;MAAEC,eAAe,CAAC,CAAChE,OAAOgE;IAAc,CAAC;AAEpD3D,UAAMK,0BAA0BjD;AAEhC,QAAID,kBAAkBC,IAAI,GAAG;AAC3BA,WAAKG,OAAM;IACb;;AAGF,MAAMqG,qBAAqB,SAArBA,oBAA+BC,uBAAuB;AAC1D,QAAMzG,OAAOmE,iBAAiB,kBAAkBsC,qBAAqB;AACrE,WAAOzG,OAAOA,OAAOA,SAAS,QAAQ,QAAQyG;;AAchD,MAAMC,kBAAkB,SAAlBA,iBAAeC,OAAoD;AAAA,QAArC7E,SAAM6E,MAAN7E,QAAQD,QAAK8E,MAAL9E,OAAK+E,mBAAAD,MAAEE,YAAAA,aAAUD,qBAAG,SAAA,QAAKA;AACnE9E,aAASA,UAAUF,gBAAgBC,KAAK;AACxCmD,wBAAmB;AAEnB,QAAI8B,kBAAkB;AAEtB,QAAIlE,MAAMG,eAAezD,SAAS,GAAG;AAInC,UAAMyH,iBAAiBpD,mBAAmB7B,QAAQD,KAAK;AACvD,UAAMmF,iBACJD,kBAAkB,IAAInE,MAAME,gBAAgBiE,cAAc,IAAI1D;AAEhE,UAAI0D,iBAAiB,GAAG;AAGtB,YAAIF,YAAY;AAEdC,4BACElE,MAAMG,eAAeH,MAAMG,eAAezD,SAAS,CAAC,EACjD+F;QACP,OAAO;AAELyB,4BAAkBlE,MAAMG,eAAe,CAAC,EAAEgC;QAC5C;iBACS8B,YAAY;AAIrB,YAAII,oBAAoBlG,UACtB6B,MAAMG,gBACN,SAAAmE,OAAA;AAAA,cAAGnC,oBAAiBmC,MAAjBnC;AAAiB,iBAAOjD,WAAWiD;QAAiB,CACzD;AAEA,YACEkC,oBAAoB,MACnBD,eAAelD,cAAchC,UAC3B6C,YAAY7C,QAAQS,OAAOqC,eAAe,KACzC,CAACW,WAAWzD,QAAQS,OAAOqC,eAAe,KAC1C,CAACoC,eAAenB,iBAAiB/D,QAAQ,KAAK,IAClD;AAOAmF,8BAAoBF;QACtB;AAEA,YAAIE,qBAAqB,GAAG;AAI1B,cAAME,wBACJF,sBAAsB,IAClBrE,MAAMG,eAAezD,SAAS,IAC9B2H,oBAAoB;AAE1B,cAAMG,mBAAmBxE,MAAMG,eAAeoE,qBAAqB;AAEnEL,4BACElB,YAAY9D,MAAM,KAAK,IACnBsF,iBAAiB/B,mBACjB+B,iBAAiB5B;QACzB,WAAW,CAAChF,WAAWqB,KAAK,GAAG;AAG7BiF,4BAAkBE,eAAenB,iBAAiB/D,QAAQ,KAAK;QACjE;MACF,OAAO;AAIL,YAAIuF,mBAAmBtG,UACrB6B,MAAMG,gBACN,SAAAuE,OAAA;AAAA,cAAGjC,mBAAgBiC,MAAhBjC;AAAgB,iBAAOvD,WAAWuD;QAAgB,CACvD;AAEA,YACEgC,mBAAmB,MAClBL,eAAelD,cAAchC,UAC3B6C,YAAY7C,QAAQS,OAAOqC,eAAe,KACzC,CAACW,WAAWzD,QAAQS,OAAOqC,eAAe,KAC1C,CAACoC,eAAenB,iBAAiB/D,MAAM,IAC3C;AAOAuF,6BAAmBN;QACrB;AAEA,YAAIM,oBAAoB,GAAG;AAIzB,cAAMF,yBACJE,qBAAqBzE,MAAMG,eAAezD,SAAS,IAC/C,IACA+H,mBAAmB;AAEzB,cAAMD,oBAAmBxE,MAAMG,eAAeoE,sBAAqB;AAEnEL,4BACElB,YAAY9D,MAAM,KAAK,IACnBsF,kBAAiBrC,oBACjBqC,kBAAiB9B;QACzB,WAAW,CAAC9E,WAAWqB,KAAK,GAAG;AAG7BiF,4BAAkBE,eAAenB,iBAAiB/D,MAAM;QAC1D;MACF;IACF,OAAO;AAGLgF,wBAAkB3C,iBAAiB,eAAe;IACpD;AAEA,WAAO2C;;AAKT,MAAMS,mBAAmB,SAAnBA,kBAA6BlH,GAAG;AACpC,QAAMyB,SAASF,gBAAgBvB,CAAC;AAEhC,QAAIsD,mBAAmB7B,QAAQzB,CAAC,KAAK,GAAG;AAEtC;IACF;AAEA,QAAIgB,eAAekB,OAAOiF,yBAAyBnH,CAAC,GAAG;AAErDhB,WAAKoI,WAAW;;;;;;;QAOdC,aAAanF,OAAOE;MACtB,CAAC;AACD;IACF;AAKA,QAAIpB,eAAekB,OAAOoF,mBAAmBtH,CAAC,GAAG;AAE/C;IACF;AAGAA,MAAEuH,eAAc;;AAOlB,MAAMC,eAAe,SAAfA,cAAyBhG,OAAO;AACpC,QAAMC,SAASF,gBAAgBC,KAAK;AACpC,QAAMiG,kBAAkBnE,mBAAmB7B,QAAQD,KAAK,KAAK;AAG7D,QAAIiG,mBAAmBhG,kBAAkBiG,UAAU;AACjD,UAAID,iBAAiB;AACnBlF,cAAMK,0BAA0BnB;MAClC;IACF,OAAO;AAELD,YAAMmG,yBAAwB;AAK9B,UAAIC;AACJ,UAAIC,sBAAsB;AAC1B,UAAItF,MAAMK,yBAAyB;AACjC,YAAI2C,YAAYhD,MAAMK,uBAAuB,IAAI,GAAG;AAElD,cAAMkF,kBAAkBxE,mBACtBf,MAAMK,uBACR;AAKA,cAAQc,gBAAkBnB,MAAME,gBAAgBqF,eAAe,EAAvDpE;AACR,cAAIA,cAAczE,SAAS,GAAG;AAE5B,gBAAM8I,YAAYrE,cAAchD,UAC9B,SAACf,MAAI;AAAA,qBAAKA,SAAS4C,MAAMK;YAAuB,CAClD;AACA,gBAAImF,aAAa,GAAG;AAClB,kBAAI7F,OAAO9B,aAAamC,MAAMU,cAAc,GAAG;AAC7C,oBAAI8E,YAAY,IAAIrE,cAAczE,QAAQ;AACxC2I,6BAAWlE,cAAcqE,YAAY,CAAC;AACtCF,wCAAsB;gBACxB;cAGF,OAAO;AACL,oBAAIE,YAAY,KAAK,GAAG;AACtBH,6BAAWlE,cAAcqE,YAAY,CAAC;AACtCF,wCAAsB;gBACxB;cAGF;YAEF;UACF;QAKF,OAAO;AAKL,cACE,CAACtF,MAAME,gBAAgBuF,KAAK,SAAClC,GAAC;AAAA,mBAC5BA,EAAEpC,cAAcsE,KAAK,SAACC,GAAC;AAAA,qBAAK1C,YAAY0C,CAAC,IAAI;aAAE;UAAA,CACjD,GACA;AAIAJ,kCAAsB;UACxB;QACF;MACF,OAAO;AAKLA,8BAAsB;MACxB;AAEA,UAAIA,qBAAqB;AACvBD,mBAAWvB,gBAAgB;;;UAGzB5E,QAAQc,MAAMK;UACd4D,YAAYtE,OAAO5B,cAAciC,MAAMU,cAAc;QACvD,CAAC;MACH;AAEA,UAAI2E,UAAU;AACZ5B,iBAAS4B,QAAQ;MACnB,OAAO;AACL5B,iBAASzD,MAAMK,2BAA2ByB,oBAAmB,CAAE;MACjE;IACF;AAEA9B,UAAMU,iBAAiBD;;AAOzB,MAAMkF,cAAc,SAAdA,aAAwB1G,OAA2B;AAAA,QAApBgF,aAAUtF,UAAAjC,SAAA,KAAAiC,UAAA,CAAA,MAAA8B,SAAA9B,UAAA,CAAA,IAAG;AAChDqB,UAAMU,iBAAiBzB;AAEvB,QAAMiF,kBAAkBJ,gBAAgB;MAAE7E;MAAOgF;IAAW,CAAC;AAC7D,QAAIC,iBAAiB;AACnB,UAAItG,WAAWqB,KAAK,GAAG;AAKrBA,cAAM+F,eAAc;MACtB;AACAvB,eAASS,eAAe;IAC1B;;AAIF,MAAM0B,WAAW,SAAXA,UAAqB3G,OAAO;AAChC,QACEzB,cAAcyB,KAAK,KACnBR,eAAekB,OAAOG,mBAAmBb,KAAK,MAAM,OACpD;AACAA,YAAM+F,eAAc;AACpBvI,WAAKoI,WAAU;AACf;IACF;AAEA,QAAIlF,OAAO9B,aAAaoB,KAAK,KAAKU,OAAO5B,cAAckB,KAAK,GAAG;AAC7D0G,kBAAY1G,OAAOU,OAAO5B,cAAckB,KAAK,CAAC;IAChD;;AAGF,MAAM4G,aAAa,SAAbA,YAAuBpI,GAAG;AAC9B,QAAMyB,SAASF,gBAAgBvB,CAAC;AAEhC,QAAIsD,mBAAmB7B,QAAQzB,CAAC,KAAK,GAAG;AACtC;IACF;AAEA,QAAIgB,eAAekB,OAAOiF,yBAAyBnH,CAAC,GAAG;AACrD;IACF;AAEA,QAAIgB,eAAekB,OAAOoF,mBAAmBtH,CAAC,GAAG;AAC/C;IACF;AAEAA,MAAEuH,eAAc;AAChBvH,MAAE2H,yBAAwB;;AAO5B,MAAMU,eAAe,SAAfA,gBAA2B;AAC/B,QAAI,CAAC9F,MAAMM,QAAQ;AACjB;IACF;AAGAhE,qBAAiBC,aAAaC,WAAWC,IAAI;AAI7CuD,UAAMQ,yBAAyBb,OAAOI,oBAClC/B,MAAM,WAAY;AAChByF,eAAS3B,oBAAmB,CAAE;IAChC,CAAC,IACD2B,SAAS3B,oBAAmB,CAAE;AAElCrC,QAAIsG,iBAAiB,WAAWd,cAAc,IAAI;AAClDxF,QAAIsG,iBAAiB,aAAapB,kBAAkB;MAClDqB,SAAS;MACTC,SAAS;IACX,CAAC;AACDxG,QAAIsG,iBAAiB,cAAcpB,kBAAkB;MACnDqB,SAAS;MACTC,SAAS;IACX,CAAC;AACDxG,QAAIsG,iBAAiB,SAASF,YAAY;MACxCG,SAAS;MACTC,SAAS;IACX,CAAC;AACDxG,QAAIsG,iBAAiB,WAAWH,UAAU;MACxCI,SAAS;MACTC,SAAS;IACX,CAAC;AAED,WAAOxJ;;AAGT,MAAMyJ,kBAAkB,SAAlBA,mBAA8B;AAClC,QAAI,CAAClG,MAAMM,QAAQ;AACjB;IACF;AAEAb,QAAI0G,oBAAoB,WAAWlB,cAAc,IAAI;AACrDxF,QAAI0G,oBAAoB,aAAaxB,kBAAkB,IAAI;AAC3DlF,QAAI0G,oBAAoB,cAAcxB,kBAAkB,IAAI;AAC5DlF,QAAI0G,oBAAoB,SAASN,YAAY,IAAI;AACjDpG,QAAI0G,oBAAoB,WAAWP,UAAU,IAAI;AAEjD,WAAOnJ;;AAOT,MAAM2J,kBAAkB,SAAlBA,iBAA4BC,WAAW;AAC3C,QAAMC,uBAAuBD,UAAUZ,KAAK,SAAUc,UAAU;AAC9D,UAAMC,eAAe3H,MAAM4H,KAAKF,SAASC,YAAY;AACrD,aAAOA,aAAaf,KAAK,SAAUrI,MAAM;AACvC,eAAOA,SAAS4C,MAAMK;MACxB,CAAC;IACH,CAAC;AAID,QAAIiG,sBAAsB;AACxB7C,eAAS3B,oBAAmB,CAAE;IAChC;;AAKF,MAAM4E,mBACJ,OAAOC,WAAW,eAAe,sBAAsBA,SACnD,IAAIC,iBAAiBR,eAAe,IACpC3F;AAEN,MAAMoG,sBAAsB,SAAtBA,uBAAkC;AACtC,QAAI,CAACH,kBAAkB;AACrB;IACF;AAEAA,qBAAiBI,WAAU;AAC3B,QAAI9G,MAAMM,UAAU,CAACN,MAAMO,QAAQ;AACjCP,YAAMC,WAAWoC,IAAI,SAAUnB,WAAW;AACxCwF,yBAAiBK,QAAQ7F,WAAW;UAClC8F,SAAS;UACTC,WAAW;QACb,CAAC;MACH,CAAC;IACH;;AAOFxK,SAAO;IACL,IAAI6D,SAAS;AACX,aAAON,MAAMM;;IAGf,IAAIC,SAAS;AACX,aAAOP,MAAMO;;IAGf2G,UAAQ,SAAAA,SAACC,iBAAiB;AACxB,UAAInH,MAAMM,QAAQ;AAChB,eAAO;MACT;AAEA,UAAM8G,aAAazG,UAAUwG,iBAAiB,YAAY;AAC1D,UAAME,iBAAiB1G,UAAUwG,iBAAiB,gBAAgB;AAClE,UAAMG,oBAAoB3G,UAAUwG,iBAAiB,mBAAmB;AAExE,UAAI,CAACG,mBAAmB;AACtBlF,4BAAmB;MACrB;AAEApC,YAAMM,SAAS;AACfN,YAAMO,SAAS;AACfP,YAAMI,8BAA8BX,IAAIwC;AAExCmF,qBAAAA,QAAAA,eAAAA,UAAAA,WAAU;AAEV,UAAMG,mBAAmB,SAAnBA,oBAAyB;AAC7B,YAAID,mBAAmB;AACrBlF,8BAAmB;QACrB;AACA0D,qBAAY;AACZe,4BAAmB;AACnBQ,2BAAAA,QAAAA,mBAAAA,UAAAA,eAAc;;AAGhB,UAAIC,mBAAmB;AACrBA,0BAAkBtH,MAAMC,WAAW2B,OAAM,CAAE,EAAE4F,KAC3CD,kBACAA,gBACF;AACA,eAAO;MACT;AAEAA,uBAAgB;AAChB,aAAO;;IAGT1C,YAAU,SAAAA,WAAC4C,mBAAmB;AAC5B,UAAI,CAACzH,MAAMM,QAAQ;AACjB,eAAO;MACT;AAEA,UAAMoH,UAAO9H,eAAA;QACX+H,cAAchI,OAAOgI;QACrBC,kBAAkBjI,OAAOiI;QACzBC,qBAAqBlI,OAAOkI;MAAmB,GAC5CJ,iBAAiB;AAGtBK,mBAAa9H,MAAMQ,sBAAsB;AACzCR,YAAMQ,yBAAyBC;AAE/ByF,sBAAe;AACflG,YAAMM,SAAS;AACfN,YAAMO,SAAS;AACfsG,0BAAmB;AAEnBvK,uBAAiBW,eAAeT,WAAWC,IAAI;AAE/C,UAAMkL,eAAehH,UAAU+G,SAAS,cAAc;AACtD,UAAME,mBAAmBjH,UAAU+G,SAAS,kBAAkB;AAC9D,UAAMG,sBAAsBlH,UAAU+G,SAAS,qBAAqB;AACpE,UAAM5C,cAAcnE,UAClB+G,SACA,eACA,yBACF;AAEAC,uBAAAA,QAAAA,iBAAAA,UAAAA,aAAY;AAEZ,UAAMI,qBAAqB,SAArBA,sBAA2B;AAC/B/J,cAAM,WAAM;AACV,cAAI8G,aAAa;AACfrB,qBAASG,mBAAmB5D,MAAMI,2BAA2B,CAAC;UAChE;AACAwH,+BAAAA,QAAAA,qBAAAA,UAAAA,iBAAgB;QAClB,CAAC;;AAGH,UAAI9C,eAAe+C,qBAAqB;AACtCA,4BACEjE,mBAAmB5D,MAAMI,2BAA2B,CACtD,EAAEoH,KAAKO,oBAAoBA,kBAAkB;AAC7C,eAAO;MACT;AAEAA,yBAAkB;AAClB,aAAO;;IAGTnL,OAAK,SAAAA,MAACoL,cAAc;AAClB,UAAIhI,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AACjC,eAAO;MACT;AAEA,UAAM2H,UAAUtH,UAAUqH,cAAc,SAAS;AACjD,UAAME,cAAcvH,UAAUqH,cAAc,aAAa;AAEzDhI,YAAMO,SAAS;AACf0H,kBAAAA,QAAAA,YAAAA,UAAAA,QAAO;AAEP/B,sBAAe;AACfW,0BAAmB;AAEnBqB,sBAAAA,QAAAA,gBAAAA,UAAAA,YAAW;AACX,aAAO;;IAGThL,SAAO,SAAAA,QAACiL,gBAAgB;AACtB,UAAI,CAACnI,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AAClC,eAAO;MACT;AAEA,UAAM8H,YAAYzH,UAAUwH,gBAAgB,WAAW;AACvD,UAAME,gBAAgB1H,UAAUwH,gBAAgB,eAAe;AAE/DnI,YAAMO,SAAS;AACf6H,oBAAAA,QAAAA,cAAAA,UAAAA,UAAS;AAEThG,0BAAmB;AACnB0D,mBAAY;AACZe,0BAAmB;AAEnBwB,wBAAAA,QAAAA,kBAAAA,UAAAA,cAAa;AACb,aAAO;;IAGTC,yBAAuB,SAAAA,wBAACC,mBAAmB;AACzC,UAAMC,kBAAkB,CAAA,EAAG5G,OAAO2G,iBAAiB,EAAElF,OAAOoF,OAAO;AAEnEzI,YAAMC,aAAauI,gBAAgBnG,IAAI,SAACrB,SAAO;AAAA,eAC7C,OAAOA,YAAY,WAAWvB,IAAIoC,cAAcb,OAAO,IAAIA;MAAO,CACpE;AAEA,UAAIhB,MAAMM,QAAQ;AAChB8B,4BAAmB;MACrB;AAEAyE,0BAAmB;AAEnB,aAAO;IACT;;AAIFpK,OAAK6L,wBAAwB/I,QAAQ;AAErC,SAAO9C;AACT;;;ACniCA,SAAS,aAAa,QAAQ,UAAU,CAAC,GAAG;AAC1C,MAAI;AACJ,QAAM,EAAE,WAAW,GAAG,iBAAiB,IAAI;AAC3C,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,WAAW,CAAC,SAAS,QAAQ,KAAK,SAAS,IAAI;AACrD,QAAM,aAAa,CAAC,SAAS,QAAQ,KAAK,WAAW,IAAI;AACzD,QAAM,QAAQ,MAAM;AAClB,QAAI,MAAM;AACR,WAAK,MAAM;AACX,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,MAAM;AACR,WAAK,QAAQ;AACb,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA;AAAA,IACE,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,OAAO;AACN,UAAI,CAAC;AACH;AACF,aAAO,gBAAgB,IAAI;AAAA,QACzB,GAAG;AAAA,QACH,aAAa;AACX,mBAAS,QAAQ;AACjB,cAAI,QAAQ;AACV,oBAAQ,WAAW;AAAA,QACvB;AAAA,QACA,eAAe;AACb,mBAAS,QAAQ;AACjB,cAAI,QAAQ;AACV,oBAAQ,aAAa;AAAA,QACzB;AAAA,MACF,CAAC;AACD,UAAI;AACF,iBAAS;AAAA,IACb;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,oBAAkB,MAAM,WAAW,CAAC;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable", "activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "getNodeForOption", "optionValue", "_len2", "_key2", "Error", "concat", "querySelector", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref2", "_ref2$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref3", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref4", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "nextNode", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkKeyNav", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "checkDomRemoval", "mutations", "isFocusedNodeRemoved", "mutation", "removedNodes", "from", "mutationObserver", "window", "MutationObserver", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pauseOptions", "onPause", "onPostPause", "unpauseOptions", "onUnpause", "onPostUnpause", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"]}