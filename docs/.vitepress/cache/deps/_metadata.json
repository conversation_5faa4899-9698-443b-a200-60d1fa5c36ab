{"hash": "835961e8", "browserHash": "d70c45e8", "optimized": {"vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "14422df0", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../../node_modules/@vue/devtools-api/lib/esm/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "5bcde62a", "needsInterop": false}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "bd3007bd", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../../node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "3c206d41", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../../node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "e7bbf97b", "needsInterop": false}}, "chunks": {"chunk-OOEHNQLD": {"file": "chunk-OOEHNQLD.js"}}}