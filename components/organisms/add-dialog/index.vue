<template>
  <div>
    <div>
      <CoreActionButton
        text="Create organism"
        color="primary"
        :icon="addIcon"
        :click="init"
      />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    Create organism
                  </DialogTitle>

                  <button @click="handleModal">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="submitForm"
                >
                  <div class="mt-2 space-y-3">
                    <div class="w-full flex items-center px-5">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="text"
                          label="Name"
                          validation="required"
                          v-model="name"
                        />
                      </div>
                    </div>
                    <div class="w-full flex items-center px-5 space-x-3">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="textarea"
                          label="Description"
                          validation="required"
                          v-model="description"
                        />
                      </div>
                    </div>

                    <div class="w-full pb-20 px-5">
                      <CoreMultiselect
                        label="Drugs"
                        v-model:items-selected="drugSelected"
                        :items="drugs"
                        mode="tags"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      :click="
                        () => {
                          clearForm();
                        }
                      "
                      text="Clear form"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  UserIcon,
  ArrowDownTrayIcon,
  ArrowUturnLeftIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Drug, Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon,
  },
  data() {
    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      name: "" as string,
      description: "" as string,
      drugSelected: null as any,
      loading: false as boolean,
      drugs: new Array<string>(),
      rawDrugs: new Array<Drug>(),
      cookie: useCookie("token"),
    };
  },

  methods: {
    async init(): Promise<void> {
      this.handleModal();

      const request: Request = {
        route: endpoints.drugs,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { pending, error, data }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.rawDrugs = data.value;

        data.value.map((item: any) => {
          this.drugs.push(item.name);
        });
      }

      if (error.value) {
        console.error(error.value);
      }
    },
    async submitForm(): Promise<void> {
      this.loading = true;
      const drugIds: number[] = this.drugSelected
        .map((name: string) => {
          const item = this.rawDrugs.find((item: Drug) => item.name === name);
          return item ? item.id : null;
        })
        .filter((id: number | null): id is number => id !== null);

      const request: Request = {
        route: endpoints.organisms,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          name: this.name,
          description: this.description,
          drugs: drugIds,
        },
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.handleModal();
        useNuxtApp().$toast.success(
          `${this.name} organism created sucessfully!`
        );
        this.name = "";
        this.description = "";
        this.drugSelected = null;
        this.loading = false;
        this.$emit("update", true);
      }

      if (error.value) {
        this.handleModal();
        console.error(error.value);
        useNuxtApp().$toast.error(ERROR_MESSAGE);
        this.loading = false;
      }
    },
    handleModal(): void {
      this.open = !this.open;
    },
    clearForm(): void {
      this.$formkit.reset("submitForm");
      this.drugSelected = null;
    },
  },
};
</script>

<style scoped></style>
