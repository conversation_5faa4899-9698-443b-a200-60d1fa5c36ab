<template>
  <div class="col-span-1 rounded border relative">
    <div v-if="!loading">
      <div
        class="flex items-center justify-between bg-gray-50 border-b px-2 py-2 rounded-t"
      >
        <h3 class="text-lg font-semibold">
          Recent Tests <span class="text-base font-medium">({{ getDepartment() }})</span>
        </h3>
      </div>
      <div>
        <table class="w-full">
          <tbody>
            <tr
              class="border-b border-dotted"
              v-for="(test, index) in tests"
              :key="index"
              :class="index % 2 !== 0 ? 'bg-gray-50' : ''"
            >
              <td class="px-2 py-2 capitalize flex items-center">
                <div
                  :class="`bg-[${getStatusColor(test.status as Statuses)}]`"
                  :style="{ backgroundColor: getColor(<Statuses>test.status) }"
                  class="w-3 h-3 rounded-full mr-2"
                ></div>
                {{
                  `${capitalizeStr(test.client.first_name.toLowerCase())}
                    ${
                      test.client.middle_name !== null
                        ? test.client.middle_name
                        : ""
                    } ${capitalizeStr(test.client.last_name.toLowerCase())}`
                }}
              </td>
              <td class="px-2 py-2">{{ test.test_type_name }}</td>
              <td class="px-2 py-2 capitalize">
                {{ test.status.split("-").join(" ") }}
              </td>
            </tr>
          </tbody>
        </table>
        <CoreActionButton
          :click="() => viewTests()"
          text="View tests &rarr;"
          color="primary"
          class="m-2 bottom-0"
        />
      </div>
    </div>
    <div v-if="loading">
      <div class="w-full flex items-center justify-between rounded-t px-2 py-2">
        <div class="h-8 w-48 bg-gray-100 animate-pulse rounded"></div>
        <div class="rounded-full h-7 w-7 bg-gray-100 animate-pulse"></div>
      </div>
      <div class="mt-2 space-y-2 px-2">
        <div class="flex items-center space-x-2" v-for="i in 10" :key="i">
          <div class="h-5 w-5 rounded-full bg-gray-100 animate-pulse"></div>
          <div class="w-full bg-gray-100 h-8 animate-pulse rounded"></div>
        </div>
      </div>
      <div class="w-32 bg-gray-100 rounded-t h-8 animate-pulse m-2"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { EllipsisVerticalIcon } from "@heroicons/vue/20/solid/index.js";
import type { Department, Request, Response, Statuses, Test } from "@/types";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import { useAuthStore } from "@/store/auth";
import { getStatusColor } from "@/utils/functions";

export default {
  components: {
    EllipsisVerticalIcon
  },
  data() {
    return {
      authStore: useAuthStore(),
      cookie: useCookie("token"),
      tests: new Array<Test>(),
      loading: false as boolean
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;
      const department: Record<string, number> =
        this.authStore.user.departments.find(
          (department: Department) =>
            department.name === this.authStore.department
        );

      const department_id: number | null = department ? department.id : null;
      const lab_location_id: number = getIdByName(
        this.authStore.locations,
        this.authStore.selectedLocation
      );

      const request: Request = {
        route: `${endpoints.tests}?minimal=true&page=1&per_page=9&status=&search=&department_id=${department_id}&lab_location=${lab_location_id}&start_date=&end_date=`,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.tests = data.value.data;
        this.loading = false;
      }

      if (error.value) {
        console.error("recent-tests: ", error.value);
        this.loading = false;
      }
    },
    viewTests(): void {
      this.$router.push("/tests");
    },
    getColor(status: Statuses): string {
      return getStatusColor(status);
    },
    getDepartment(): string {
      const { department } = useAuthStore()
      return department == 'Lab Reception' ? 'All' : department
    }
  },
  watch: {
    authStore: {
      handler(_newValue, _oldValue) {
        this.init();
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
