<template>
  <div>
    <div>
      <CoreActionButton text="New lab section" color="primary" :icon="addIcon" :click="adjustVisibility" />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="adjustVisibility" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">

                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    Add new lab section
                  </DialogTitle>

                  <button @click="adjustVisibility">
                    <XMarkIcon class="w-5 h-5" />
                  </button>

                </div>

                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false" #default="{ value }"
                  id="submitForm">

                  <div class="mt-2 space-y-3">
                    <div class="w-full flex items-center px-5">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit type="text" label="Name" validation="required" v-model="name" />
                      </div>
                    </div>
                  </div>

                  <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton text="Clear form" type="button" :click="(() => { clearForm() })" />
                    <CoreActionButton :loading="loading" type="submit" :click="(() => { })" color="success"
                      :icon="saveIcon" text="Save changes" />
                  </div>

                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { PlusIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Request, Response } from '@/types';

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon
  },
  data() {

    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      name: "" as string,
      description: "" as string,
      loading: false as boolean,
      cookie: useCookie('token')
    }
  },
  methods: {

    async submitForm(): Promise<void> {

      this.loading = true;

      const request: Request = {
        route: endpoints.departments,
        method: 'POST',
        token: `${this.cookie}`,
        body: {
          'name': this.name
        }
      };

      const { pending, error, data }: Response = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {

        this.adjustVisibility()

        useNuxtApp().$toast.success(`${this.name} laboratory section created successfully!`);

        this.name = '';

        this.loading = false;

        this.$emit('update', true);
      }

      if (error.value) {

        this.adjustVisibility()

        console.log(error.value)

        useNuxtApp().$toast.error(ERROR_MESSAGE);

        this.loading = false;
      }
    },
    adjustVisibility(): void {
      this.open = !this.open
    },
    clearForm(): void {
      this.$formkit.reset('submitForm');
    }

  }
}
</script>

<style>
</style>
