<template>
  <div>
    <CoreActionButton
      :click="init"
      color="primary"
      text="View"
      :icon="viewIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-semibold leading-6"
                  >
                    <img
                      src="~assets/icons/cone_test_on_nets.svg"
                      class="w-8 h-8 mr-2"
                    />
                    Test Type Details
                  </DialogTitle>

                  <button
                    @click="
                      () => {
                        handleDialog();
                      }
                    "
                  >
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div
                  v-show="loading"
                  class="flex items-center justify-center mx-auto my-20"
                >
                  <CoreLoader :loading="loading" />
                </div>

                <div v-show="!loading" class="space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Name</label>
                    <p>{{ details?.name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Short Name</label>
                    <p>{{ details?.short_name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg"
                      >Laboratory Section</label
                    >
                    <p>{{ details?.department?.name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Specific Sex</label>
                    <p>{{ details.sex }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg"
                      >Compatible Specimen</label
                    >
                    <div class="flex flex-wrap gap-2">
                      <p v-for="i in details.specimens" :key="i">
                        {{ `${i.name}` }}
                      </p>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Organisms</label>
                    <div class="flex flex-wrap gap-2">
                      <p v-for="(i, index) in details.organisms" :key="i">
                        {{ `${i.name}`
                        }}<span v-if="index !== details.organisms.length - 1"
                          >,</span
                        >
                      </p>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Measure</label>
                    <p v-for="i in details.indicators" :key="i">
                      {{ `${i.name}` }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg"
                      >Expected Turn Around Time</label
                    >
                    <p>
                      {{
                        `${details.expected_turn_around_time?.value} ${details.expected_turn_around_time?.unit}`
                      }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Date Created</label>
                    <p>{{ moment(details.created_date).format(DATE_FORMAT) }}</p>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts" setup>
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import moment from "moment";
import type { Request, Response, TestType } from "@/types";
import fetchRequest from "@/services/fetch";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const viewIcon = ArrowTopRightOnSquareIcon;
const show = ref(false);
const loading = ref(false);
const details = ref<TestType | any>({});
const cookie = useCookie("token");

const init = async (): Promise<void> => {
  handleDialog();
  loading.value = true;
  const request: Request = {
    route: `${endpoints.viewTestType}/${props.data.id}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    details.value = data.value;
    loading.value = false;
  }
  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(`An error occurred, please try again!`);
  }
};

const handleDialog = (): void => {
  show.value = !show.value;
};
</script>

<style>
</style>
