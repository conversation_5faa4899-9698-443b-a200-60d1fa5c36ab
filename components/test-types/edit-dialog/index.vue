<template>
  <div>
    <div>
      <CoreActionButton text="Edit" color="success" :icon="editIcon" :click="loadDetails" />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="adjustVisibility" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-gray-900 bg-opacity-50"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-5xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    <img src="@/assets/icons/cone_test_on_nets.svg" class="w-8 h-8 mr-2" />
                    Edit Test Type
                  </DialogTitle>

                  <button @click="adjustVisibility">
                    <XMarkIcon class="w-5 h-5" />
                  </button>

                </div>



              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'

import { PlusIcon, XMarkIcon, UserIcon, CheckCircleIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon, InformationCircleIcon, ChevronUpDownIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js';

import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Request, Response, Autocomplete, Department, Indicator, IndicatorRange, TestIndicatorType, TestType } from '@/types';

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
    XMarkIcon,
    UserIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    ChevronUpDownIcon
  },
  data() {

    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      loadingSpecimens: false as boolean,
      showMeasures: false as boolean,
      measures: 1 as number,
      ranges: 0 as number,
      indicators: [{ name: "", test_indicator_type: 0, unit: "", description: "", indicator_ranges: [] as Array<IndicatorRange | Autocomplete> }] as Array<Indicator>,
      numericRange: { max_age: 0, min_age: 0, lower_range: 0, upper_range: 0, interpretation: "" } as IndicatorRange,
      autocompleteRange: { value: "", interpretation: "" } as Autocomplete,
      name: "" as string,
      shortName: "" as string,
      turnAroundTime: "" as string,
      measureTypes: Array<TestIndicatorType>(),
      measureSelected: { name: '', id: 0 },
      loading: false as boolean,
      updating: false as boolean,
      editIcon: PencilSquareIcon,
      id: this.data.id as unknown as number,
      details: {
        name: "",
        short_name: "",
        expected_turn_around_time: {
          value: "",
          unit: ""
        },
        test_indicator_type: {
          id: 0,
          name: ""
        },
        department: {
          id: 0,
          name: ""
        },
        specimens: {
          id: 0,
          name: "",
          created_date: ""
        },
        indicators: [],
        organisms: []
      } as TestType | any,
      specimensSelected: new Array<string>(),
      rawOrganisms: new Array<{}>(),
      organisms: new Array<string>(),
      organismsSelected: new Array<String>(),
      shouldPrintResults: false as boolean,
      showCultureWorksheet: false as boolean,
      timeTypes: new Array({ name: "Month" }, { name: "Weeks" }, { name: "Days" }, { name: "Hours" }, { name: "Minutes" }),
      duration: { name: "-- select value --" },
      expected_turn_around_time: '' as string,
      cookie: useCookie('token'),
      rawSpecimens: new Array<String>(),
      specimens: new Array<String>(),
      departments: new Array<Department>(),
      selectedDepartment: { name: "-- select value --" } as Department,
      measureType: "" as string

    }
  },
  props: {
    data: {
      type: Object,
      required: true,
    }
  },
  methods: {
    /**
     * @method loadSpecimens loads test type details by id
     * @params null
     * @return promise @typeof void
     */
    async loadDetails(): Promise<void> {
      await this.loadSpecimens();
      await this.loadDepartments();
      await this.loadTestTypeIndicators();
      await this.loadOrganisms();

      this.open = true;
      this.loading = true;

      const request: Request = {
        route: `${endpoints.viewTestType}/${this.data.id}`,
        method: "GET",
        token: `${this.cookie}`
      }

      const { data, error, pending }: Response = await fetchRequest(request);

      if (data.value) {
        this.details = data.value;
        this.specimensSelected = reverseFilterArrays(this.details.specimens);
        this.organismsSelected = reverseFilterArrays(this.details.organisms);
        this.selectedDepartment = this.details.department_id;
        this.showCultureWorksheet = this.details.organisms.length > 0;
        this.duration = { name: this.details.expected_turn_around_time?.unit }
        this.expected_turn_around_time = this.details.expected_turn_around_time?.value;
        this.loading = false;
      }

      if (error.value) {
        console.error("error:", error.value);
        this.loading = false;
      }

    },
    async loadOrganisms() {
      const request: Request = {
        route: endpoints.organisms,
        method: "GET",
        token: `${this.cookie}`
      }
      const { pending, error, data }: Response = await fetchRequest(request);
      if (data.value) {
        this.rawOrganisms = data.value
        data.value.map((organism: { name: string }) => {
          this.organisms.push(organism.name)
        })
      }
      if (error.value) {
        console.log(error.value)
      }
    },

    /**
     * @function loadSpecimens loads specimens from api @endpoints
     */
    async loadSpecimens() : Promise<void> {
      const request : Request = {
        route: endpoints.specimens,
        method: 'GET',
        token: `${this.cookie}`
      }
      const { data, pending, error }: Response = await fetchRequest(request);
      this.loadingSpecimens = pending;
      if (data.value) {

        this.loadingSpecimens = false;

        this.rawSpecimens = data.value;

        data.value.map((specimen: { name: string; }) => {
          this.specimens.push(specimen.name)
        });

      };

      if(error.value){

        this.loadingSpecimens = false;

        console.error(error.value);

      }

    },

    /**
     * @function loadDepartments load departments from api @endpoints
     */
    async loadDepartments() {

      const request: Request = {
        route: endpoints.departments,
        method: "GET",
        token: `${this.cookie}`
      }
      const { data, error }: Response = await fetchRequest(request);

      if (data.value) {
        this.departments = data.value;
        this.selectedDepartment = data.value[0];
      };

      if (error.value) {
        console.error("error:", error.value);
      };

    },

    async loadTestTypeIndicators() {

      const request: Request = {
        route: `${endpoints.testTypes}/test_indicator_types`,
        method: "GET",
        token: `${this.cookie}`
      }
      const { data, error }: Response = await fetchRequest(request);

      if (data.value) {
        this.measureTypes = data.value;
        this.measureSelected = data.value[0];
        this.measureType = this.measureSelected.name;
      };

      if(error.value){
        console.error(error.value);
      }
    },

    /**
     * @method adjustVisibility change modal visibility
     */
    adjustVisibility(): void {
      this.open = !this.open;
    },

    getEmitedDepartment(department: Department): void {
      this.selectedDepartment = department;
    },
    /**
     * @method getDuration get duration
     * @param value any
     * @returns void
     */
    getDuration(value: any): void {
      this.duration = value;
    },

    /**
     * @method addMeasure creates a new indicator object
     * @returns null
     */
    addMeasure(indicators: Array<Object>): any {
      indicators.push({
        name: "" as string,
        test_indicator_type: this.measureSelected,
        unit: "" as string,
        description: "" as string,
        indicator_ranges: [] as Array<any>
      });
    },

    /**
     *
     * @param index current indicator array
     */
    addRange(index: number, indicators: any) {

      this.ranges = this.ranges + 1;

      if (this.measureSelected.name == "Numeric") {
        try {
          indicators.indicator_ranges.push(this.numericRange)
        } catch (error) {
          console.error("error:", error)
        }
      } else if (this.measureSelected.name === "Auto Complete") {
        try {
          indicators.indicator_ranges.push({
            value: "", interpretation: ""
          })
        } catch (error) {
          console.error("error:", error)
        }
      }

    },

    /**
     *
     * @param raw original array of specimen objects from api
     * @param items filtered array of specimen names from raw
     * @returns filtered array of specimens id for api @POST
     */
    filterSpecimens(raw: Array<any>, items: Array<String>): Array<Number> {

      let filteredSpecimens = new Array<Number>();

      items.map((name) => {
        const obj = raw.filter(obj => obj.name === name);
        filteredSpecimens.push(obj[0].id)
      });

      return filteredSpecimens;
    },

    async submitForm(): Promise<void> {

      this.updating = true;
      this.details.specimens = this.filterSpecimens(this.rawSpecimens, this.specimensSelected);
      this.details.department_id = this.selectedDepartment.id as unknown as string;
      this.details.organisms = this.showCultureWorksheet ? filterArrays(this.rawOrganisms, this.organismsSelected) : new Array();
      this.details.expected_turn_around_time = {
        value: this.expected_turn_around_time,
        unit: this.duration.name
      }
      this.details.print_device = this.shouldPrintResults

      const request: Request = {
        route: `${endpoints.testTypes}/${this.data.id}`,
        method: 'PUT',
        token: `${this.cookie}`,
        body: this.details
      }
      const { pending, data, error }: Response = await fetchRequest(request)
      this.updating = pending;
      if (data.value) {
        this.open = false;
        useNuxtApp().$toast.success(`Test type updated successfully!`);
        this.updating = false;
        this.$emit('update', true);
      }

      if (error.value) {
        useNuxtApp().$toast.error(`An error occurred, please try again!`);
        console.error("error:", error.value)
        this.updating = false;

      }

    },
    removeObjectKeys(measure: string, key: string) : void {
      if(measure == 'rich_text' || measure == 'free_text'){
        delete this.details[key];
      }
    }
  },

  watch: {
    /**
     * @method measureSelected assigns measure on current selected measure
     */
    measureSelected: {
      handler(newValue, oldValue) {
      },
      deep: true
    }
  }
}
</script>

<style>
</style>
