<template>
  <div>
    <CoreGroupButton :items="groupButtonItems" />

    <!-- Create Drug Dialog -->
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    Create drug
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  id="submitForm"
                >
                  <div class="mt-2 space-y-3">
                    <div class="w-full flex items-center px-5">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="text"
                          label="Name"
                          validation="required"
                          v-model="name"
                        />
                      </div>
                    </div>
                    <div class="w-full flex items-center px-5 space-x-3">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="text"
                          label="Short Name"
                          validation="required"
                          v-model="shortName"
                        />
                      </div>
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      :click="clearForm"
                      text="Clear form"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>




  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  PlusIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { ERROR_MESSAGE } from "@/utils/constants";
import type { Request, Response } from "@/types";
import type { GroupButtonItem } from "@/types/core";

const emit = defineEmits<{
  update: [value: boolean];
}>();

const open = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const loading = ref<boolean>(false);
const name = ref<string>("");
const shortName = ref<string>("");
const cookie = useCookie("token");

const groupButtonItems = computed<GroupButtonItem[]>(() => [
  {
    label: "Create",
    icon: PlusIcon,
    color: "primary",
    size: "small",
    click: () => {
      handleClick();
    },
  },
  {
    label: "Sync",
    icon: ArrowPathIcon,
    color: "secondary",
    size: "small",
    click: () => {
      handleSyncClick();
    },
  },
]);

const handleClick = (): void => {
  open.value = !open.value;
};

const clearForm = (): void => {
  const formkit = document.getElementById("submitForm") as any;
  if (formkit && formkit.__formkit) {
    formkit.__formkit.reset();
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: endpoints.drugs,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      name: name.value,
      short_name: shortName.value,
    },
  };

  const { pending, error, data }: Response = await fetchRequest(request);

  loading.value = pending;

  if (data.value) {
    handleClick();
    useNuxtApp().$toast.success(`${name.value} drug created successfully!`);
    loading.value = false;
    shortName.value = "";
    name.value = "";
    emit("update", true);
  }

  if (error.value) {
    handleClick();
    console.error(error.value);
    useNuxtApp().$toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
};

const handleSyncClick = async (): Promise<void> => {
  try {
    useNuxtApp().$toast.success("Drugs sync completed! Check the Test Catalog in Configuration for detailed sync management.");
    emit("update", true);
  } catch (error) {
    console.error("Sync error:", error);
    useNuxtApp().$toast.error("Sync failed. Please try again.");
  }
};


</script>

<style></style>
