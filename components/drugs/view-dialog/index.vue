<template>
    <div>

        <CoreActionButton :click="handleClick" color="primary" text="View" :icon="viewIcon"/>

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95"
                        >
                            <DialogPanel
                            class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                            >

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle
                                        as="h3"
                                        class="text-lg flex items-center font-medium leading-6"
                                    >
                                        <img src="~assets/icons/medicines.svg" class="w-8 h-8 mr-2"/>
                                        View drug
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5"/>
                                    </button>

                                </div>

                            <div class="space-y-3 px-5 py-5">

                                <div class="w-full flex flex-col space-y-1">
                                    <label class="font-semibold text-lg">Name</label>
                                    <p class="underline">{{ data.name }}</p>
                                </div>

                                <div class="w-full flex flex-col space-y-1">
                                    <label class="font-semibold text-lg">Short Name</label>
                                    <p class="underline">{{ data.short_name }}</p>
                                </div>

                                <div class="w-full flex flex-col space-y-1">
                                    <label class="font-semibold text-lg">Date Created</label>
                                    <p class="underline">{{ moment(data.created_date).format(DATE_FORMAT) }}</p>
                                </div>

                            </div>

                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';

import { XMarkIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js';

import moment from 'moment';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data(){

        return{
            viewIcon: ArrowTopRightOnSquareIcon,
            show: false as boolean,
            editIcon: PencilSquareIcon,
            moment: moment
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        handleClick(){
            this.show = !this.show
        }
    }
}
</script>

<style>

</style>
