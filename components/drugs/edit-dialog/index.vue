<template>
    <div>
        <CoreActionButton :click="init" text="Edit" color="success" :icon="editIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        Edit drug
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" id="editForm" submit-label="Update" @submit="submitForm"
                                    :actions="false" #default="{ value }">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Name" validation="required" v-model="name" />
                                            </div>
                                        </div>
                                        <div class="w-full flex items-center px-5 space-x-3">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Short Name" validation="required"
                                                    v-model="shortName" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { clearForm() })"
                                            text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Request, Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            editIcon: PencilSquareIcon as Object,
            show: false as boolean,
            saveIcon: ArrowDownTrayIcon as Object,
            name: '' as string,
            shortName: '' as string,
            loading: false as boolean,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        async init () : Promise<void> {
            this.handleClick();
            this.loading = true;
            const request: Request = {
                route: `${endpoints.drugs}/${this.data.id}`,
                method: 'GET',
                token: `${this.cookie}`
            }

            const { pending, error, data }: Response = await fetchRequest(request);
            this.loading = pending;
            if (data.value) {
                this.shortName = data.value.short_name
                this.name = data.value.name

            }

            if (error.value) {
                console.log(error.value)

            }
        },
        async submitForm(): Promise<void> {

            this.loading = true;

            const request: Request = {
                route: `${endpoints.drugs}/${this.data.id}`,
                method: 'PUT',
                token: `${this.cookie}`,
                body: {
                    "name": this.name,
                    "short_name": this.shortName
                }
            }

            const { pending, error, data }: Response = await fetchRequest(request);
            this.loading = pending;
            if (data.value) {
                this.handleClick()
                useNuxtApp().$toast.success(`Drug updated successfully!`);
                this.loading = false;
                this.$emit('update', true);
            }

            if (error.value) {

                this.handleClick()

                useNuxtApp().$toast.error(ERROR_MESSAGE);

                console.log(error.value)

                this.loading = false;
            }
        },
        /**
         * @method clearForm removes values from inputs
         * @returns void
         * @params null
         */
        clearForm(): void {
            this.$formkit.reset('editForm');
        },
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
