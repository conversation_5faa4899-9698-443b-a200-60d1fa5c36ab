<template>
  <aside
    class="fixed inset-y-0 z-10 flex flex-col flex-shrink-0 w-64 max-h-screen overflow-y-auto transition-all transform bg-white border-r shadow-lg lg:z-auto lg:static lg:shadow-none"
    :class="{ '-translate-x-full lg:translate-x-0 lg:w-20': !menu }"
    x-transition:enter="transition transform duration-300"
    x-transition:enter-start="-translate-x-full opacity-30 ease-in"
    x-transition:enter-end="translate-x-0 opacity-100 ease-out" x-transition:leave="transition transform duration-300"
    x-transition:leave-start="translate-x-0 opacity-100 ease-out"
    x-transition:leave-end="-translate-x-full opacity-0 ease-in">
    <div class="flex items-center space-x-5 px-5 border-b object-cover sticky top-0 bg-white z-20">
      <img src="@/assets/images/logo.png" alt="app-logo" class="w-10 h-10" />
      <h3 class="text-xl font-semibold py-3 truncate uppercase">
        {{ appName }}
      </h3>
    </div>
    <nuxt-link to="/home" :class="$route.name == 'home' || $route.name == 'configuration'
      ? 'bg-sky-500 text-white px-5 py-3 flex items-center transition duration-150 w-full'
      : 'w-full hover:bg-sky-500 hover:text-white transition duration-150 flex items-center space-x-2 pl-5 py-3 border-b'
      ">
      <HomeIcon class="w-5 h-5 mr-2" />
      <p v-show="menu">Home</p>
    </nuxt-link>
    <Disclosure v-slot="{ open }">
      <DisclosureButton v-show="disabled" :class="$route.name && $route?.name?.toString().includes('sample-entry')
        ? 'w-full text-left bg-sky-500 text-white flex transition duration-150'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <FolderPlusIcon class="w-5 h-5 mr-2" />
            <p class="text-left line-clamp-1" v-show="menu">Sample Entry</p>
          </button>
        </span>
      </DisclosureButton>
      <DisclosurePanel v-show="disabled" class="px-12 pt-2 pb-2 text-sm border-b">
        <nuxt-link v-for="(sampleEntry, index) in routes.sampleEntries" :key="index" :to="sampleEntry.href">
          <button class="flex items-center space-x-2 mb-3 hover:text-sky-500 hover:font-medium">
            <img src="@/assets/icons/virus_lab_research_test_tube.svg" class="w-5 h-5" />
            <p>{{ sampleEntry.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>

    <nuxt-link v-show="disabled" to="/worksheets" :class="$route.name && $route?.name?.toString().includes('worksheets')
      ? 'bg-sky-500 text-white px-5 py-3 flex items-center transition duration-150 w-full'
      : 'w-full hover:bg-sky-500 hover:text-white transition duration-150 flex items-center space-x-2 pl-5 py-3 border-b'
      ">
      <Square3Stack3DIcon class="w-5 h-5 mr-2" />
      <p v-show="menu">Worksheets</p>
    </nuxt-link>

    <nuxt-link v-if="can.manage('patients')" to="/patients" :class="$route.name == 'patients'
      ? 'bg-sky-500 text-white px-5 py-3 flex items-center transition duration-150 w-full'
      : 'w-full hover:bg-sky-500 hover:text-white transition duration-150 flex items-center space-x-2 pl-5 py-3 border-b'
      ">
      <UserGroupIcon class="w-5 h-5 mr-2" />
      <p v-show="menu">Patients</p>
    </nuxt-link>

    <nuxt-link to="/tests" :class="$route.name &&
      ($route?.name?.toString() === 'tests' ||
        $route?.fullPath?.toString().includes('tests/'))
      ? 'bg-sky-500 text-white px-5 py-3 flex items-center transition duration-150 w-full'
      : 'w-full hover:bg-sky-500 hover:text-white transition duration-150 flex items-center space-x-2 pl-5 py-3 border-b'
      ">
      <BeakerIcon class="w-5 h-5 mr-2" />
      <p v-show="menu">Tests</p>
    </nuxt-link>

    <Disclosure v-slot="{ open }" v-if="can.manage('lab_configurations')">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('lab-configuration')
        ? 'w-full text-left bg-sky-500 text-white flex transition duration-150'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <WrenchScrewdriverIcon class="w-5 h-5 mr-2" />
            <p v-show="menu" class="text-left line-clamp-1">
              Lab Configuration
            </p>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="px-12 pt-2 pb-2 text-sm">
        <nuxt-link v-for="(configuration, index) in routes.labConfigurations" :key="index" :to="configuration.href">
          <button class="flex items-center space-x-2 mb-3 hover:text-sky-500 hover:font-medium">
            <FolderIcon class="w-6 h-6" />
            <p v-show="menu">{{ configuration.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>

    <Disclosure v-slot="{ open }" v-if="can.manage('test_catalog')">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('test-catalog')
        ? 'bg-sky-500 text-white flex items-center transition duration-150 w-full'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <TableCellsIcon class="w-5 h-5 mr-2" />
            <p v-show="menu">Test Catalog</p>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="px-12 pt-2 pb-2 text-sm">
        <nuxt-link v-for="(catalog, index) in routes.testCatalogues" :key="index" :to="catalog.href">
          <button class="flex items-center space-x-2 mb-3 hover:text-sky-500 hover:font-medium">
            <FolderIcon class="w-6 h-6" />
            <p class="truncate line-clamp-1">{{ catalog.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>

    <Disclosure v-slot="{ open }" v-if="can.view('reports')">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('reports-')
        ? 'bg-sky-500 text-white flex items-center transition duration-150 w-full'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <DocumentChartBarIcon class="w-5 h-5 mr-2" />
            <p v-show="menu">Reports</p>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="pt-2 pb-2 text-sm">
        <Disclosure v-slot="{ open }" v-for="(report, index) in routes.reports" :key="index">
          <DisclosureButton
            class="flex w-full items-center justify-between text-left text-sm border-b border-dotted hover:text-white hover:bg-sky-500 focus:outline-none focus:ring-none">
            <span>
              <button class="truncate px-12 text-left hover:bg-sky-500 transition duration-150 py-3">
                {{ report.type }}
              </button>
            </span>
            <ChevronUpIcon :class="[
              !menu ? 'w-7 h-7 mr-2' : 'w-5 h-5 mr-2',
              open ? '' : 'rotate-180 transform'
            ]" />
          </DisclosureButton>
          <DisclosurePanel>
            <nuxt-link v-for="(item, index) in report.items" :key="index" :to="item.href">
              <button :class="$route.path.split('/').length > 3 &&
                $route.path
                  .split('/')[3]
                  .toString()
                === item.name.replaceAll(' ', '-').toLowerCase() &&
                'w-full border-l-4 border-sky-500 bg-sky-100 py-3'
                " class="pl-12 flex items-center space-x-2 mb-2 hover:text-sky-500 hover:font-medium py-2">
                <img src="@/assets/icons/report.png" alt="report-icon" class="w-6 h-6 mr-2" />
                <p class="text-left">{{ item.name }}</p>
              </button>
            </nuxt-link>
          </DisclosurePanel>
        </Disclosure>
      </DisclosurePanel>
    </Disclosure>

    <Disclosure v-slot="{ open }" v-if="can.manage('users')">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('access-controls')
        ? 'w-full text-left bg-sky-500 text-white flex transition duration-150'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button
            class="w-full hover:bg-sky-500 transition duration-150 flex items-center justify-between space-x-2 pl-5 py-3">
            <div class="flex space-x-2 items-center">
              <Cog8ToothIcon class="w-5 h-5 mr-2" />
              <p v-show="menu" class="text-left line-clamp-1">
                Access Controls
              </p>
            </div>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="px-12 pt-2 pb-2 text-sm">
        <nuxt-link v-for="(control, index) in routes.accessControls" :key="index" :to="control.href">
          <button class="flex items-center space-x-2 mb-3 hover:text-sky-500 hover:font-medium">
            <FolderIcon class="w-6 h-6" />
            <p>{{ control.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>

    <Disclosure v-slot="{ open }" v-if="can.view('stock_management') || can.manage('stock_management')">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('stock-management')
        ? 'w-full text-left bg-sky-500 text-white flex transition duration-150'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <InboxIcon class="w-5 h-5 mr-2" />
            <p v-show="menu" class="text-left line-clamp-1">Stock Management</p>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="pt-2 pb-2 text-sm">
        <nuxt-link v-for="(item, index) in routes.stock" :key="index" :to="item.href">
          <button :class="currentPath.toLowerCase() == item.name.toLowerCase()
            ? 'w-full items-center space-x-2 px-12 flex bg-sky-100 border-l-4 border-sky-500 py-2.5 hover:text-sky-500 transition duration-150'
            : 'flex items-center space-x-2 py-2.5 px-12 w-full hover:text-sky-500 hover:font-medium'
            ">
            <component :is="item.icon" class="w-5 h-5" />
            <p>{{ item.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>
    <Disclosure v-slot="{ open }">
      <DisclosureButton :class="$route.name && $route?.name?.toString().includes('help-support')
        ? 'w-full text-left bg-sky-500 text-white flex transition duration-150'
        : 'flex w-full border-b justify-between text-left text-sm hover:text-white hover:bg-sky-500 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75'
        ">
        <span class="w-full flex items-center">
          <button class="w-full hover:bg-sky-500 transition duration-150 flex items-center space-x-2 pl-5 py-3">
            <LifebuoyIcon class="w-5 h-5 mr-2" />
            <p v-show="menu" class="text-left line-clamp-1">Help & Support</p>
          </button>
          <ChevronUpIcon v-show="menu" class="w-5 h-5 mr-2" :class="[open ? '' : 'rotate-180 transform'
          ]" />
        </span>
      </DisclosureButton>
      <DisclosurePanel class="pt-2 pb-2 text-sm">
        <nuxt-link v-for="(item, index) in routes.helpSupport" :key="index" :to="item.href">
          <button :class="currentPath.toLowerCase() == item.name.toLowerCase() &&
            'w-full border-l-4 border-sky-500 bg-sky-100 py-3'
            " class="pl-12 flex items-center space-x-2 mb-2 hover:text-sky-500 hover:font-medium py-2">
            <component :is="item.icon" class="w-5 h-5" />
            <p>{{ item.name }}</p>
          </button>
        </nuxt-link>
      </DisclosurePanel>
    </Disclosure>
    <div class="w-full mt-auto sticky">
      <div class="flex items-center justify-center space-x-2 bg-gray-50 rounded px-2 py-2 border border-gray-200"
        :class="menu ? '' : 'm-0'">
        <div class="text-gray-500 relative">
          <div v-if="nlimsStatus.is_running"
            class="absolute top-0 right-0 w-3 h-3 transition-all duration-150 bg-green-500 rounded-full ring-2 ring-green-400 border-2 border-green-300 animate-pulse">
          </div>
          <svg v-else class="absolute -top-2 -right-1 w-5 h-5 text-red-500 transition-all duration-150 animate-pulse"
            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path fill="currentColor"
              d="M1 21L12 2l11 19zm11-3q.425 0 .713-.288T13 17t-.288-.712T12 16t-.712.288T11 17t.288.713T12 18m-1-3h2v-5h-2z" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24">
            <g fill="none">
              <path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M22 19h-8M2 19h8m2-2v-3"
                opacity="0.5" />
              <circle cx="12" cy="19" r="2" stroke="currentColor" stroke-width="1.5" />
              <path stroke="currentColor" stroke-width="1.5"
                d="M2 11a3 3 0 0 1 3-3h14a3 3 0 1 1 0 6H5a3 3 0 0 1-3-3Zm0-6a3 3 0 0 1 3-3h14a3 3 0 1 1 0 6H5a3 3 0 0 1-3-3Z" />
              <path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M13 5h6m-6 6h6" opacity="0.5" />
              <circle cx="6" cy="5" r="1" fill="currentColor" opacity="0.5" />
              <circle cx="6" cy="11" r="1" fill="currentColor" opacity="0.5" />
            </g>
          </svg>
        </div>
        <div v-show="menu" class="relative" :class="nlimsStatus.is_running ? 'text-green-500' : 'text-red-500'">
          <p class="font-medium text-sm">{{ nlimsStatus.is_running_text }}</p>
          <div class="-mt-1">
            <svg v-if="nlimsStatus.is_authenticated" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24">
              <path fill="currentColor"
                d="M11.71 10.33C11.01 8.34 9.11 7 7 7c-2.76 0-5 2.24-5 5s2.24 5 5 5c2.11 0 4.01-1.34 4.71-3.33l.23-.67H18v4h2v-4h2v-2H11.94zM7 15c-1.65 0-3-1.35-3-3s1.35-3 3-3s3 1.35 3 3s-1.35 3-3 3"
                opacity="0.3" />
              <path fill="currentColor"
                d="M7 5c-3.86 0-7 3.14-7 7s3.14 7 7 7c2.72 0 5.17-1.58 6.32-4H16v4h6v-4h2V9H13.32C12.17 6.58 9.72 5 7 5m15 8h-2v4h-2v-4h-6.06l-.23.67C11.01 15.66 9.11 17 7 17c-2.76 0-5-2.24-5-5s2.24-5 5-5c2.11 0 4.01 1.34 4.71 3.33l.23.67H22zM7 9c-1.65 0-3 1.35-3 3s1.35 3 3 3s3-1.35 3-3s-1.35-3-3-3m0 4c-.55 0-1-.45-1-1s.45-1 1-1s1 .45 1 1s-.45 1-1 1" />
            </svg>
            <svg v-else class="text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24">
              <g fill="none">
                <path fill="currentColor" fill-opacity="0.25"
                  d="M4 12c0-.943 0-1.414.293-1.707S5.057 10 6 10h12c.943 0 1.414 0 1.707.293S20 11.057 20 12v6.038c0 .38 0 .571-.029.74a2 2 0 0 1-1.164 1.49c-.156.07-.341.116-.71.208c-1.238.31-1.857.464-2.476.578c-2.394.44-4.848.44-7.243 0c-.618-.114-1.237-.269-2.474-.578c-.37-.092-.555-.139-.71-.207a2 2 0 0 1-1.165-1.492C4 18.61 4 18.42 4 18.037z" />
                <path stroke="currentColor" d="M16.5 10V9a4.5 4.5 0 0 0-9 0v1" />
                <circle cx="12" cy="15" r="2" fill="currentColor" />
                <path stroke="currentColor" stroke-linecap="round" d="M12 16v2.5" />
              </g>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue"
import {
  ChevronUpIcon,
  FolderIcon,
} from "@heroicons/vue/20/solid"
import {
  HomeIcon,
  BeakerIcon,
  WrenchScrewdriverIcon,
  TableCellsIcon,
  DocumentChartBarIcon,
  Cog8ToothIcon,
  UserGroupIcon,
  FolderPlusIcon,
  Square3Stack3DIcon,
  InboxIcon,
  LifebuoyIcon
} from "@heroicons/vue/24/solid"

import Package from "@/package.json"

const props = defineProps<{
  menu: boolean
}>()

const appName = Package.name;
const { $nlims } = useNuxtApp()
const route = useRoute()
const { can } = usePermissions();
const disabled = ref(false)

const currentPath = computed(() => {
  const segments = route.path.split("/")
  return segments[segments.length - 1].replace(/-/g, " ")
})

const nlimsStatus = computed(() => ({
  is_running: ($nlims as { is_running?: boolean })?.is_running || false,
  is_running_text: ($nlims as { is_running?: boolean })?.is_running
    ? 'NLIMS Connected'
    : 'NLIMS Disconnected',
  is_authenticated: ($nlims as { is_authenticated?: boolean })?.is_authenticated || false
}))

defineExpose({
  appName: Package.name,
  package: Package,
  nlimsStatus
})
</script>

<style></style>
