<template>
  <nav
    class="bg-white border-b px-5 flex items-center justify-between flex-shrink-0 py-1"
  >
    <button @click="handleMenuClick">
      <Bars3BottomLeftIcon class="w-6 h-6" />
    </button>

    <div class="flex flex-wrap items-center bg-white">
      <div class="-mr-5">
        <Menu as="div" class="relative inline-block text-left">
          <div>
            <MenuButton
              class="inline-flex items-center w-full justify-center rounded-tl rounded-bl bg-gray-100 pl-2 pr-8 py-1.5 font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75"
            >
              <img
                src="@/assets/icons/hospital.svg"
                class="w-7 h-7 object-cover"
              />
              {{ selectedLocation }}
              <ChevronUpDownIcon
                class="-mr-1 ml-2 h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </MenuButton>
          </div>

          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems
              class="absolute right-0 mt-0 w-48 origin-top-right divide-y divide-gray-100 rounded bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"
            >
              <div class="px-1 py-1">
                <MenuItem
                  v-slot="{ active }"
                  v-for="location in locations"
                  v-bind:key="location.name"
                  @click="updateLocation(location)"
                >
                  <button
                    :class="[
                      active ? 'bg-gray-100' : 'text-gray-900',
                      'group flex w-full items-center px-2 py-2 border-b border-dotted',
                    ]"
                  >
                    <img
                      src="@/assets/icons/hospital.svg"
                      class="w-5 h-5 object-cover mr-2"
                    />
                    {{ location.name }}
                  </button>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>

      <div class="w-44 bg-white relative">
        <CoreDropdown :items="departments" v-model="selectedDepartment" />
      </div>

      <Menu as="div" class="relative inline-block text-left bg-white">
        <div>
          <MenuButton>
            <button class="px-2 py-2 flex items-center bg-white">
              <img
                src="@/assets/images/user.png"
                class="w-7 h-7 mr-2 object-cover rounded-full"
                alt="user-icon"
              />
              <div class="flex items-center space-x-2">
                <p class="font-medium capitalize">{{ username }}</p>
                <ChevronDownIcon class="w-4 h-4" />
              </div>
            </button>
          </MenuButton>
        </div>

        <transition
          enter-active-class="transition duration-100 ease-out"
          enter-from-class="transform scale-95 opacity-0"
          enter-to-class="transform scale-100 opacity-100"
          leave-active-class="transition duration-75 ease-in"
          leave-from-class="transform scale-100 opacity-100"
          leave-to-class="transform scale-95 opacity-0"
        >
          <MenuItems
            style="z-index: 10000"
            class="absolute right-0 w-48 origin-top-right divide-y divide-gray-100 rounded-bl rounded-br bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="px-1 py-1">
              <MenuItem v-slot="{ active }">
                <button
                  :class="[
                    active ? 'bg-gray-50' : 'text-gray-900',
                    'group flex w-full items-center px-2 py-2',
                  ]"
                  @click="$router.push('/settings')"
                >
                  <Cog6ToothIcon class="mr-2 h-5 w-5" />
                  Settings
                </button>
              </MenuItem>
            </div>
            <div class="px-1 py-1">
              <MenuItem v-slot="{ active }">
                <button
                  :class="[
                    active ? 'bg-gray-50' : 'text-gray-900',
                    'group flex w-full items-center px-2 py-2',
                  ]"
                  @click="$router.push('/configuration')"
                >
                  <ComputerDesktopIcon class="mr-2 h-5 w-5" />
                  Configuration
                </button>
              </MenuItem>
            </div>
            <div class="px-1 py-1">
              <MenuItem v-slot="{ active }">
                <button
                  @click="logout"
                  :class="[
                    active ? 'bg-gray-50' : 'text-gray-900',
                    'group flex w-full items-center px-2 py-2',
                  ]"
                >
                  <ArrowRightOnRectangleIcon class="mr-2 h-5 w-5" />
                  Logout
                </button>
              </MenuItem>
            </div>
          </MenuItems>
        </transition>
      </Menu>
    </div>
  </nav>
</template>

<script setup lang="ts">
import {
  Menu,
  MenuButton,
  MenuItems,
  MenuItem,
} from "@headlessui/vue";

import {
  ChevronDownIcon,
  ChevronUpDownIcon,
  Cog6ToothIcon,
  Bars3BottomLeftIcon,
  ComputerDesktopIcon,
  ArrowRightOnRectangleIcon
} from "@heroicons/vue/24/solid";

import { useAuthStore } from "@/store/auth";
import type { Department, DropdownItem } from "@/types";

interface Props {
  click?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  click: () => {}
})

const authStore = useAuthStore();
const { $toast } = useNuxtApp();
const { lastKnownRoute } = useRouteStore();

const username = ref(authStore.user.username);
const departments = ref<Department[]>(authStore.user.departments);
const selectedDepartment = ref<Department>({ name: authStore.department });
const locations = ref<DropdownItem[]>(authStore.locations);
const selectedLocation = ref(authStore.selectedLocation);

const updateDepartment = (value: Department): void => {
  authStore.department = value.name
  $toast.success(`Changed laboratory section to ${value.name}`)
}

const updateLocation = (value: DropdownItem): void => {
  authStore.selectedLocation = value.name
  $toast.success(`Changed laboratory location to ${value.name}`)
  selectedLocation.value = value.name
}

const handleMenuClick = (): void => {
  if (props.click) {
    props.click()
  } else {
    console.warn("undefined:", "click event")
  }
}

const logout = (): void => {
  const route = useRoute();

  // Capture current route before logout
  if (route.path && route.path !== '/' && route.path !== '/login') {
    lastKnownRoute(route.path);
  }

  authStore.logUserOut()
}

watch(selectedDepartment, (value) => {
  updateDepartment(value)
}, { deep: true })
</script>

<style></style>
