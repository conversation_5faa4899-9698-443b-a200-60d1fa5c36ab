<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowDownTrayIcon,
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Response, Request } from "@/types";

type TData = {
  specimen_name: string;
  test_type_name: string;
  life_span_units: string;
  life_span: string;
};

interface ITimeUnits {
  name: string;
  value: string;
}

type TSpecimenLifespan = {
  specimen_name?: string;
  life_span: string;
  test_type?: string;
  life_span_units: string;
};

const emit = defineEmits(["action-completed"]);
const props: Readonly<
  Omit<
    {
      id: number;
    },
    never
  > & {}
> = defineProps<{
  id: number;
}>();

const show: Ref<boolean> = ref<boolean>(false);
const loading: Ref<boolean> = ref<boolean>(false);
const cookie: Ref<string> = useCookie("token");
const { $toast } = useNuxtApp();
const body: Ref<TSpecimenLifespan> = ref<TSpecimenLifespan>({
  life_span: "",
  life_span_units: "",
});

const data: Ref<TData> = ref<TData>({
  specimen_name: "",
  test_type_name: "",
  life_span: "",
  life_span_units: "",
});
const timeUnits: Ref<ITimeUnits[]> = ref<ITimeUnits[]>([
  { name: "mins", value: "mins" },
  { name: "hours", value: "hours" },
  { name: "days", value: "days" },
  { name: "months", value: "months" },
]);

const loadSpecimenLifespan = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.specimensLifespan.edit}/${props.id}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const v: Response = await fetchRequest(request);

  if (v.data.value) {
    const row: Record<string, any> = v.data.value;
    body.value.life_span = row.life_span;
    body.value.life_span_units = row.life_span_units;
    data.value.specimen_name = row.specimen.name;
    data.value.test_type_name = row.test_type.name;
    show.value = true;
  }

  loading.value = false;

  if (v.error.value) {
    loading.value = false;
    $toast.error(`${ERROR_MESSAGE}`);
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
	route: `${endpoints.specimensLifespan.update}/${props.id}`,
	method: "PUT",
	token: `${cookie.value}`,
	body: body.value
  }

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    handleClick();
    emit("action-completed", true);
    $toast.success(`Specimen lifespan updated successfully!`);
  }

  if (error.value) {
    $toast.success(`An error occurred, please try again!`);
    handleClick();
  }
};

const handleClick = (): void => {
  show.value = !show.value;
  loading.value = false;
};
</script>

<template>
  <div>
    <CoreActionButton
      :click="loadSpecimenLifespan"
      color="success"
      text="Edit"
      :icon="PencilSquareIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/medical_sample.svg"
                      class="w-8 h-8 mr-2"
                      alt="test-tube-icon"
                    />
                    Edit Specimen Lifespan
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div v-show="!loading" class="mt-2 space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">{{
                      "Specimen Name"
                    }}</label>
                    <p class="text-base text-gray-600">{{ data.specimen_name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">{{
                      "Test Type"
                    }}</label>
                    <p class="text-base text-gray-600">{{ data.test_type_name }}</p>
                  </div>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{}"
                >
                  <div class="mt-2 space-y-3 px-5 relative pb-20">
                    <FormKit
                      class="w-full"
                      type="number"
                      label="Lifespan"
                      validation="required"
                      v-model="body.life_span"
                    />

                    <CoreMultiselect
                      :label="'Duration'"
                      mode="single"
                      :items="timeUnits.map((unit) => unit.value)"
                      v-model:items-selected="body.life_span_units"
					  class="z-10"
                    />
                    
                  </div>
				  <div
                      class="mt-10 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                    >
                      <CoreActionButton
                        type="submit"
                        :click="() => {}"
                        color="success"
                        :loading="loading"
                        :icon="ArrowDownTrayIcon"
                        text="Save changes"
                      />
                    </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>
