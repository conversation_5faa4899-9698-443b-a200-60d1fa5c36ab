<template>
    <div>
        <Popover v-slot="{ close }" class="relative">
            <PopoverButton class="bg-gray-50 text-zinc-500 flex items-center space-x-2 rounded border px-2 py-1.5">
                <svg class="mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 48 48">
                    <path fill="currentColor" fill-rule="evenodd"
                        d="M29 6v2c0 1.306.835 2.418 2 2.83V12h-1a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1h-1v-1h5v12.066H20.11a.3.3 0 0 1-.218-.09l-.366-.37l.369-.367a3.77 3.77 0 0 0 .001-5.333l-1.8-1.8a3.77 3.77 0 0 0-5.333-.003l-.704.704a3.95 3.95 0 0 0-3.892 1.021a4.034 4.034 0 0 0 0 5.676l.833.839V34H6v2h3.05a3.5 3.5 0 1 0 4.899 0h20.102a3.5 3.5 0 1 0 4.899 0H42v-2h-4v-3h.066C40.24 31 42 29.224 42 27.033a3.97 3.97 0 0 0-2-3.455V11a2 2 0 0 0-2-2h-6a1 1 0 0 1-1-1V6zM13.826 16.868l4.29 4.32l.365-.364a1.77 1.77 0 0 0 0-2.504l-1.8-1.8a1.77 1.77 0 0 0-2.504-.002zM36 34H11v-8.644l5.275 5.311c.212.213.498.333.797.333H36z"
                        clip-rule="evenodd" />
                </svg>
                Filter By Wards
            </PopoverButton>

            <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                <PopoverPanel
                    class="absolute z-10 mt-0 w-screen bg-white border shadow-2xl rounded max-w-2xl transform px-4 sm:px-0 lg:max-w-3xl">
                    <div class="px-5 py-3">
                        <CoreMultiselect placeholder="click to select wards" label="Select ward(s)" mode="tags" :items="computedWards" v-model:items-selected="selectedWards"/>
                    </div>
                    <div class="flex items-end justify-end space-x-2 border-t px-4 py-2">
                            <CoreActionButton text="Apply" :icon="ArrowUturnRightIcon" color="primary" :click="() => { onApply(close)}" />
                        </div>
                </PopoverPanel>
            </transition>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import type { Ward } from '@/types';
import { ArrowUturnRightIcon } from '@heroicons/vue/24/solid';

const emits = defineEmits(['update:items-selected']);
const props = defineProps({
    wards: {
        type: Array<Ward>,
        required: true
    }
});

const computedWards = computed((): string[] => {
    return props.wards.map((ward) => ward.name);
});

const selectedWards = ref<string[]>([]);

const onApply = (close: Function): void => {
    emits('update:items-selected', selectedWards.value);
    close();
}
</script>