<template>
  <div>
    <div>
      <CoreActionButton
        :click="init"
        text="View"
        color="primary"
        :icon="viewIcon"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="() => {}" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/emergency_post.svg"
                      class="w-8 h-8 mr-2"
                    />
                    View Test Panel
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div
                  v-show="loading"
                  class="flex items-center justify-center mx-auto my-20"
                >
                  <CoreLoader :loading="loading" />
                </div>

                <div v-show="!loading" class="space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Name</label>
                    <p class="underline">{{ details.name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Short Name</label>
                    <p class="underline">{{ details.short_name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Description</label>
                    <p class="underline">{{ details.description }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold list text-lg">Test Types</label>
                    <div
                      v-for="test in details.test_types"
                      :key="test.id"
                      class="flex flex-wrap underline"
                    >
                      {{ test.name }}
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  ArrowTopRightOnSquareIcon,
  XMarkIcon,
  UserIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Response, TestPanel } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon,
  },
  data() {
    return {
      open: false,
      viewIcon: ArrowTopRightOnSquareIcon,
      loading: false as boolean,
      details: <TestPanel>{},
      cookie: useCookie("token"),
    };
  },
  props: {
    data: {
      required: true,
      type: Object,
    },
  },
  methods: {
    async init(): Promise<void> {
      this.open = true;
      this.loading = true;
      const { pending, error, data }: Response = await fetchRequest({
        route: `${endpoints.testPanels}/${this.data.id}`,
        method: "GET",
        token: `${this.cookie}`,
      });
      this.loading = pending;
      if (data.value) {
        this.details = data.value;
        this.loading = false;
      }
      if (error.value) {
        console.error(error.value);
        this.loading = false;
        useNuxtApp().$toast.error(`An error occurred, please try again!`);
      }
    },
    handleClick() {
      this.open = !this.open;
    },
  },
};
</script>
