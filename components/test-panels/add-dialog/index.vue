<template>
  <div>
    <div>
      <CoreActionButton
        :click="handleClick"
        text="Create test panel"
        color="primary"
        :icon="addIcon"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-20">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    Create Test Panel
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="mt-2 space-y-3 px-5">
                    <div class="w-full flex">
                      <FormKit
                        type="text"
                        label="Name"
                        validation="required"
                        v-model="name"
                      />
                    </div>

                    <div class="w-full flex">
                      <FormKit
                        type="text"
                        label="Short Name"
                        validation="required"
                        v-model="shortName"
                      />
                    </div>

                    <div class="w-full">
                      <CoreMultiselect
                        label="Select Test Types"
                        :items="testTypes"
                        v-model:items-selected="testTypesSelected"
                        mode="tags"
                      />
                    </div>

                    <div class="w-full flex">
                      <FormKit
                        type="textarea"
                        label="Description"
                        validation="required"
                        v-model="description"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      text="Dismiss"
                      type="button"
                      :click="handleClick"
                    />
                    <CoreActionButton
                      type="submit"
                      :click="() => {}"
                      :loading="loading"
                      :icon="saveIcon"
                      text="Save changes"
                      color="success"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  UserIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon,
  },
  data() {
    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      name: "" as string,
      shortName: "" as string,
      description: "" as string,
      testTypes: new Array<string>(),
      testTypesSelected: new Array<string>(),
      rawTestTypes: new Array<{ id: number; name: string }>(),
      loading: false as boolean,
      cookie: useCookie("token"),
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init(): Promise<void> {
      const request: Request = {
        route: endpoints.testTypes,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error }: Response = await fetchRequest(request);

      if (data.value) {
        const { test_types } = data.value;
        this.rawTestTypes = test_types;
        this.testTypes = test_types.map((test: { name: string }) => test.name);
      }

      if (error.value) {
        console.error(error.value);
      }
    },
    async submitForm(): Promise<void> {
      this.loading = true;
      const testTypesId: number[] = this.testTypesSelected
        .map((name: string) => {
          const item = this.rawTestTypes.find((item) => item.name === name);
          return item ? item.id : null;
        })
        .filter((id: number | null): id is number => id !== null);

      const request: Request = {
        route: endpoints.testPanels,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          name: this.name,
          short_name: this.shortName,
          description: this.description,
          test_types: testTypesId,
        },
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.handleClick();
        useNuxtApp().$toast.success(`Test panel created successfully!`);
        this.$emit("update", true);
      }

      if (error.value) {
        useNuxtApp().$toast.error(ERROR_MESSAGE);
        console.error(error.value);
        this.handleClick();
      }
    },
    handleClick(): void {
      this.open = !this.open;
    },
  },
};
</script>

<style></style>
