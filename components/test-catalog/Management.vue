<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium text-gray-900">
          Test Catalog Management
        </h3>
        <p class="text-sm text-gray-500">
          Manage test catalog versions and synchronization with NLIMS
        </p>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button
          v-for="(tab, index) in tabs"
          :key="tab.id"
          @click="activeTab = index"
          :class="[
            activeTab === index
              ? 'border-sky-500 text-sky-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="py-4">
      <!-- Versions Tab -->
      <div v-if="activeTab === 0" class="space-y-6">
        <!-- Current Version Section -->
        <div class="bg-white border border-gray-200 rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-medium text-gray-900">Current Version</h4>
            <p class="text-sm text-gray-500">
              Currently active test catalog version
            </p>
          </div>
          <div v-if="currentVersion" class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    Version {{ currentVersion.version }}
                  </p>
                  <p class="text-sm text-gray-500">
                    Created: {{ formatDate(currentVersion.created_at) }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  Active
                </span>
              </div>
            </div>
            <div
              v-if="currentVersion.stats"
              class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4"
            >
              <div class="text-center p-4 bg-gray-50 rounded-lg">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ currentVersion.stats.organisms }}
                </p>
                <p class="text-sm text-gray-500">Organisms</p>
              </div>
              <div class="text-center p-4 bg-gray-50 rounded-lg">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ currentVersion.stats.specimens }}
                </p>
                <p class="text-sm text-gray-500">Specimens</p>
              </div>
              <div class="text-center p-4 bg-gray-50 rounded-lg">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ currentVersion.stats.drugs }}
                </p>
                <p class="text-sm text-gray-500">Drugs</p>
              </div>
              <div class="text-center p-4 bg-gray-50 rounded-lg">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ currentVersion.stats.testTypes }}
                </p>
                <p class="text-sm text-gray-500">Test Types</p>
              </div>
            </div>
          </div>
          <div
            v-else
            class="px-6 py-4 flex flex-col items-center justify-center space-y-2"
          >
            <CoreLoader :height="80" :width="80" />
            <p class="text-sm text-gray-500">Loading version information...</p>
          </div>
        </div>

        <div class="bg-white border border-gray-200 rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-medium text-gray-900">
              Available Versions
            </h4>
            <p class="text-sm text-gray-500">
              Test catalog versions available for activation
            </p>
          </div>
          <div v-if="loadingVersions" class="px-6 py-8">
            <div class="flex flex-col items-center justify-center">
              <CoreLoader :height="80" :width="80" />
              <span class="ml-2 text-sm text-gray-500"
                >Loading versions...</span
              >
            </div>
          </div>
          <div v-else-if="availableVersions.length === 0" class="px-6 py-8">
            <p class="text-center text-sm text-gray-500">
              No versions available
            </p>
          </div>
          <div v-else class="divide-y divide-gray-200">
            <div
              v-for="version in availableVersions"
              :key="version.id"
              class="px-6 py-4 hover:bg-gray-50"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      :class="[
                        'w-3 h-3 rounded-full',
                        version.status === 'active'
                          ? 'bg-green-500'
                          : version.status === 'available'
                          ? 'bg-blue-500'
                          : 'bg-gray-400',
                      ]"
                    ></div>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      Version {{ version.version }}
                    </p>
                    <p class="text-sm text-gray-500">
                      Created: {{ formatDate(version.created_at) }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-3">
                  <span
                    :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      version.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : version.status === 'available'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800',
                    ]"
                  >
                    {{
                      version.status === "active"
                        ? "Active"
                        : version.status === "available"
                        ? "Available"
                        : "Draft"
                    }}
                  </span>
                  <button
                    v-if="version.status === 'available'"
                    @click="activateVersion(version)"
                    :disabled="activating"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    <ArrowPathIcon
                      v-if="activating"
                      class="animate-spin -ml-1 mr-1 h-3 w-3"
                    />
                    {{ activating ? "Activating..." : "Activate" }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sync Tab -->
      <div v-if="activeTab === 1" class="space-y-6">
        <!-- Sync Status Section -->
        <div class="bg-white border border-gray-200 rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-lg font-medium text-gray-900">Sync Status</h4>
                <p class="text-sm text-gray-500">
                  Current synchronization status with NLIMS
                </p>
              </div>
              <button
                @click="checkForNewVersion"
                :disabled="checkingForUpdates"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                <ArrowPathIcon
                  v-if="checkingForUpdates"
                  class="animate-spin -ml-1 mr-1 h-3 w-3"
                />
                {{ checkingForUpdates ? "Checking..." : "Check for Updates" }}
              </button>
            </div>
          </div>
          <div v-if="lastSync" class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <div
                    :class="[
                      'w-4 h-4 rounded-full',
                      lastSync.status === 'success'
                        ? 'bg-green-500'
                        : lastSync.status === 'error'
                        ? 'bg-red-500'
                        : lastSync.status === 'checking'
                        ? 'bg-blue-500'
                        : 'bg-yellow-500',
                    ]"
                  ></div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    {{
                      lastSync.status === "success"
                        ? "Successfully synced"
                        : lastSync.status === "error"
                        ? "Sync failed"
                        : lastSync.status === "checking"
                        ? "Checking for updates"
                        : "Sync in progress"
                    }}
                  </p>
                  <p class="text-sm text-gray-500">
                    {{
                      lastSync.message ||
                      `Last sync: ${formatDate(lastSync.timestamp)}`
                    }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    lastSync.status === 'success'
                      ? 'bg-green-100 text-green-800'
                      : lastSync.status === 'error'
                      ? 'bg-red-100 text-red-800'
                      : lastSync.status === 'checking'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-yellow-100 text-yellow-800',
                  ]"
                >
                  {{
                    lastSync.status === "success"
                      ? "Up to date"
                      : lastSync.status === "error"
                      ? "Failed"
                      : lastSync.status === "checking"
                      ? "Checking"
                      : "Syncing"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div v-else class="px-6 py-4">
            <p class="text-sm text-gray-500">Loading sync status...</p>
          </div>
        </div>

        <!-- Sync Actions Section -->
        <div class="bg-white border border-gray-200 rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-medium text-gray-900">Sync Actions</h4>
            <p class="text-sm text-gray-500">
              Synchronize test catalog data with NLIMS
            </p>
          </div>
          <div class="px-6 py-4 space-y-4">
            <!-- Full Sync -->
            <div
              class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
            >
              <div>
                <h5 class="text-sm font-medium text-gray-900">Full Sync</h5>
                <p class="text-sm text-gray-500">
                  Synchronize all test catalog data including organisms,
                  specimens, drugs, and test types
                </p>
              </div>
              <button
                @click="performFullSync"
                :disabled="syncing"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                <ArrowPathIcon
                  v-if="syncing"
                  class="animate-spin -ml-1 mr-2 h-4 w-4"
                />
                {{ syncing ? "Syncing..." : "Start Full Sync" }}
              </button>
            </div>

            <!-- Selective Sync Options -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">Organisms</h5>
                  <button
                    @click="performSelectiveSync('organisms')"
                    :disabled="syncing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    Sync
                  </button>
                </div>
                <p class="text-xs text-gray-500">
                  Last synced:
                  {{
                    syncHistory.organisms
                      ? formatDate(syncHistory.organisms)
                      : "Never"
                  }}
                </p>
              </div>

              <div class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">Specimens</h5>
                  <button
                    @click="performSelectiveSync('specimens')"
                    :disabled="syncing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    Sync
                  </button>
                </div>
                <p class="text-xs text-gray-500">
                  Last synced:
                  {{
                    syncHistory.specimens
                      ? formatDate(syncHistory.specimens)
                      : "Never"
                  }}
                </p>
              </div>

              <div class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">Drugs</h5>
                  <button
                    @click="performSelectiveSync('drugs')"
                    :disabled="syncing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    Sync
                  </button>
                </div>
                <p class="text-xs text-gray-500">
                  Last synced:
                  {{
                    syncHistory.drugs ? formatDate(syncHistory.drugs) : "Never"
                  }}
                </p>
              </div>

              <div class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">Test Types</h5>
                  <button
                    @click="performSelectiveSync('test_types')"
                    :disabled="syncing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    Sync
                  </button>
                </div>
                <p class="text-xs text-gray-500">
                  Last synced:
                  {{
                    syncHistory.testTypes
                      ? formatDate(syncHistory.testTypes)
                      : "Never"
                  }}
                </p>
              </div>

              <div class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">
                    Lab Test Sites
                  </h5>
                  <button
                    @click="performSelectiveSync('lab_test_sites')"
                    :disabled="syncing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
                  >
                    Sync
                  </button>
                </div>
                <p class="text-xs text-gray-500">
                  Last synced:
                  {{
                    syncHistory.labTestSites
                      ? formatDate(syncHistory.labTestSites)
                      : "Never"
                  }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sync Progress Dialog -->
    <TestCatalogSyncDialog
      :show="showSyncDialog"
      :progress="syncProgress"
      :status="syncStatus"
      :sync-type="syncType"
      @cancel="cancelSync"
    />
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon } from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";

interface VersionStats {
  organisms: number;
  specimens: number;
  drugs: number;
  testTypes: number;
}

interface Version {
  id: number;
  version: string;
  status: "active" | "available" | "draft";
  created_at: string;
  lastUpdated?: string;
  stats?: VersionStats;
}

interface SyncStatus {
  status: "success" | "error" | "pending" | "checking";
  timestamp: string;
  message?: string;
}

interface SyncHistory {
  organisms: string | null;
  specimens: string | null;
  drugs: string | null;
  testTypes: string | null;
  labTestSites: string | null;
}

type SyncSection =
  | "test_types"
  | "specimens"
  | "organisms"
  | "lab_test_sites"
  | "drugs";

interface Tab {
  id: string;
  name: string;
}

const tabs: Tab[] = [
  { id: "versions", name: "Versions" },
  { id: "sync", name: "Sync" },
];

const activeTab = ref<number>(0);
const { $toast }: any = useNuxtApp();

const loadingVersions = ref<boolean>(false);
const checkingForUpdates = ref<boolean>(false);

const currentVersion = ref<Version | null>(null);
const availableVersions = ref<Version[]>([]);
const activating = ref<boolean>(false);

const lastSync = ref<SyncStatus | null>(null);
const syncHistory = ref<SyncHistory>({
  organisms: null,
  specimens: null,
  drugs: null,
  testTypes: null,
  labTestSites: null,
});

const syncing = ref<boolean>(false);
const showSyncDialog = ref<boolean>(false);
const syncProgress = ref<number>(0);
const syncStatus = ref<string>("");
const syncType = ref<string>("");

const { executeCancellableRequest, cancelRequest } = useCancellableRequest();

const formatDate = (dateString: string): string => {
  return moment(dateString).format("MMM DD, YYYY HH:mm");
};

const activateVersion = async (version: Version): Promise<void> => {
  activating.value = true;

  try {
    const request: Request = {
      route: `${endpoints.checkTestCatalogVersion}?version=${version.version}`,
      method: "GET",
    };

    const { data, error }: Response = await fetchRequest(request);

    if (data.value) {
      currentVersion.value = { ...version, status: "active" };

      availableVersions.value = availableVersions.value.map((v) => ({
        ...v,
        status:
          v.id === version.id
            ? ("active" as const)
            : v.status === "active"
            ? ("available" as const)
            : v.status,
      }));

      $toast.success(`Version ${version.version} activated successfully!`);
    }

    if (error.value) {
      console.error("Failed to activate version:", error.value);
      $toast.error("Failed to activate version. Please try again.");
    }
  } finally {
    activating.value = false;
  }
};

const performFullSync = async (): Promise<void> => {
  if (!currentVersion.value) {
    $toast.error("No version selected for sync");
    return;
  }
  syncType.value = "full";
  await startSync();
};

const performSelectiveSync = async (section: SyncSection): Promise<void> => {
  if (!currentVersion.value) {
    $toast.error("No version selected for sync");
    return;
  }
  syncType.value = section;
  await startSync();
};

const checkForNewVersion = async (): Promise<void> => {
  checkingForUpdates.value = true;
  lastSync.value = {
    status: "checking",
    timestamp: new Date().toISOString(),
    message: "Checking for updates...",
  };

  try {
    await fetchVersions();
    lastSync.value = {
      status: "success",
      timestamp: new Date().toISOString(),
      message: "Version list updated",
    };
    $toast.success("Version list updated");
  } catch (error) {
    console.error("Failed to check for updates:", error);
    lastSync.value = {
      status: "error",
      timestamp: new Date().toISOString(),
      message: "Failed to check for updates",
    };
    $toast.error("Failed to check for updates");
  } finally {
    checkingForUpdates.value = false;
  }
};

const fetchVersions = async (): Promise<void> => {
  loadingVersions.value = true;

  try {
    const request: Request = {
      route: endpoints.testCatalogVersions,
      method: "GET",
    };

    const { data, error }: Response = await fetchRequest(request);

    if (data.value) {
      const versions = data.value as Version[];
      availableVersions.value = versions.map((version) => ({
        ...version,
        status: "available" as const,
      }));

      if (versions.length > 0) {
        const latestVersion = versions.reduce((latest, current) =>
          new Date(current.created_at) > new Date(latest.created_at)
            ? current
            : latest
        );

        currentVersion.value = {
          ...latestVersion,
          status: "active" as const,
        };

        availableVersions.value = availableVersions.value.map((version) => ({
          ...version,
          status:
            version.id === latestVersion.id
              ? ("active" as const)
              : ("available" as const),
        }));
      }
    }

    if (error.value) {
      console.error("Failed to fetch versions:", error.value);
      $toast.error("Failed to load test catalog versions");
    }
  } finally {
    loadingVersions.value = false;
  }
};

const getSyncStatusByProgress = (progress: number): string => {
  const sectionName =
    syncType.value === "full"
      ? "test catalog"
      : getSectionDisplayName(syncType.value);

  const statusMap = [
    { threshold: 20, status: "Connecting to NLIMS..." },
    { threshold: 40, status: `Fetching ${sectionName} data...` },
    { threshold: 60, status: "Processing data..." },
    { threshold: 80, status: "Updating local database..." },
    { threshold: Infinity, status: "Finalizing sync..." },
  ];

  return (
    statusMap.find((item) => progress < item.threshold)?.status ||
    "Finalizing sync..."
  );
};

const getSectionDisplayName = (section: string): string => {
  const displayNames: Record<string, string> = {
    organisms: "organisms",
    specimens: "specimens",
    drugs: "drugs",
    test_types: "test types",
    lab_test_sites: "lab test sites",
  };

  return displayNames[section] || section;
};

const startSync = async (): Promise<void> => {
  syncing.value = true;
  showSyncDialog.value = true;
  syncProgress.value = 0;
  syncStatus.value = "Initializing sync...";

  let progressInterval: NodeJS.Timeout | null = null;

  try {
    progressInterval = setInterval(() => {
      if (syncProgress.value < 90) {
        syncProgress.value += Math.random() * 10;
        syncStatus.value = getSyncStatusByProgress(syncProgress.value);
      }
    }, 500);

    const request: Request = {
      route: endpoints.syncTestCatalog,
      method: "POST",
      body:
        syncType.value === "full"
          ? {
              version: currentVersion.value?.version,
              sections: "",
            }
          : {
              version: currentVersion.value?.version,
              sections: syncType.value,
            },
    };

    const { error, data }: Response = await executeCancellableRequest(request);
    if (progressInterval) clearInterval(progressInterval);

    if (data.value) {
      syncProgress.value = 100;
      syncStatus.value = "Sync completed successfully!";

      $toast.success("Test catalog synced successfully!");
      lastSync.value = {
        status: "success",
        timestamp: new Date().toISOString(),
        message: "Sync completed successfully",
      };

      if (syncType.value === "full") {
        const now = new Date().toISOString();
        syncHistory.value = {
          organisms: now,
          specimens: now,
          drugs: now,
          testTypes: now,
          labTestSites: now,
        };
      } else {
        const sectionMap: Record<SyncSection, keyof SyncHistory> = {
          organisms: "organisms",
          specimens: "specimens",
          drugs: "drugs",
          test_types: "testTypes",
          lab_test_sites: "labTestSites",
        };

        const historyKey = sectionMap[syncType.value as SyncSection];
        if (historyKey) {
          syncHistory.value[historyKey] = new Date().toISOString();
        }
      }

      await fetchVersions();
    }

    if (error.value) {
      if (progressInterval) clearInterval(progressInterval);
      syncProgress.value = 0;
      syncStatus.value = "Sync failed";

      console.error(error.value);
      $toast.error("Sync failed. Please try again.");
      lastSync.value = {
        status: "error",
        timestamp: new Date().toISOString(),
        message: "Sync failed",
      };
    }
  } catch (error) {
    if (progressInterval) clearInterval(progressInterval);
    syncProgress.value = 0;
    syncStatus.value = "Sync failed";

    console.error("Sync error:", error);

    if (error instanceof Error && error.name === "AbortError") {
      $toast.warning("Sync cancelled");
      lastSync.value = {
        status: "error",
        timestamp: new Date().toISOString(),
        message: "Sync cancelled",
      };
    } else {
      $toast.error("Sync failed. Please try again.");
      lastSync.value = {
        status: "error",
        timestamp: new Date().toISOString(),
        message: "Sync failed",
      };
    }
  } finally {
    setTimeout(() => {
      showSyncDialog.value = false;
      syncing.value = false;
      syncProgress.value = 0;
      syncStatus.value = "";
    }, 2000);
  }
};

const cancelSync = (): void => {
  cancelRequest();
  syncing.value = false;
  showSyncDialog.value = false;
  syncProgress.value = 0;
  syncStatus.value = "";
  lastSync.value = {
    status: "error",
    timestamp: new Date().toISOString(),
    message: "Sync cancelled by user",
  };
};

onMounted(async () => {
  await fetchVersions();
  if (!lastSync.value) {
    lastSync.value = {
      status: "success",
      timestamp: new Date().toISOString(),
      message: "Ready to sync",
    };
  }
});
</script>
