<template>
  <div class="px-5 py-5">
    <h3 class="text-2xl font-semibold flex items-center">
      <img src="~assets/icons/desktop_app.svg" class="w-8 h-8 mr-2" />
      IBLIS Configuration
    </h3>

    <div class="mt-5 font-medium text-center text-gray-500 bg-gray-50">
      <ul class="flex flex-wrap -mb-px">
        <li
          @click="tab = index"
          class="mr-2"
          v-for="(item, index) in filteredTabs"
          :key="index"
        >
          <a
            href="#"
            :class="
              tab == index
                ? 'inline-block py-2 px-4 text-white bg-sky-500 active dark:text-sky-500 dark:border-sky-500'
                : 'inline-block p-2 border-b-2 border-transparent rounded-t-lg hover:text-sky-500 hover:border-sky-500'
            "
          >
            {{ item }}
          </a>
        </li>
      </ul>
    </div>

    <div class="py-5">
      <div v-if="tab == 0">
        <FormKit
          type="form"
          submit-label="Update"
          @submit="submitForm"
          :actions="false"
          #default=""
        >
          <div class="grid grid-cols-3 gap-4">
            <FormKit label="Name" type="text" v-model="name"></FormKit>

            <FormKit label="Code" type="text" v-model="code"></FormKit>

            <FormKit label="District" type="text" v-model="district"></FormKit>

            <FormKit label="Address" type="text" v-model="address"></FormKit>

            <FormKit label="Phone Number" type="text" v-model="phone"></FormKit>
          </div>
          <div class="mt-4">
            <CoreActionButton
              :click="() => {}"
              type="submit"
              :loading="loading"
              :icon="saveIcon"
              text="Save Changes"
              color="success"
            ></CoreActionButton>
          </div>
        </FormKit>
      </div>
      <div v-if="tab == 1">
        <div class="flex items-center justify-end mb-5">
          <CorePrinterAdd @update="loadPrinters" />
        </div>

        <div
          v-show="loadingPrinters"
          class="flex items-center mx-auto justify-center py-20"
        >
          <CoreLoader :loading="loadingPrinters"></CoreLoader>
        </div>

        <div v-show="!loadingPrinters" class="relative overflow-x-auto">
          <table class="w-full text-left border rounded-lg">
            <thead class="uppercase bg-gray-100">
              <tr>
                <th
                  class="uppercase py-2 px-2"
                  v-for="(printer, index) in printersHeader"
                  :key="index"
                >
                  {{ printer.text }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                class="bg-white border-b"
                v-for="(printer, index) in printers"
                :key="index"
              >
                <th class="px-2 py-2 font-normal">
                  {{ printer.name }}
                </th>
                <td class="px-2 py-2">
                  {{ printer.description }}
                </td>
                <td class="px-2 py-2">
                  {{ moment(printer.created_date).format(DATE_FORMAT) }}
                </td>
                <td>
                  <div class="flex items-center space-x-2">
                    <CorePrinterEditDialog
                      :data="printer"
                      @update="loadPrinters"
                    >
                    </CorePrinterEditDialog>
                    <CorePrinterDeleteDialog
                      :data="printer"
                      @update="loadPrinters"
                    >
                    </CorePrinterDeleteDialog>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div v-if="tab == 2">
        <!-- Loading state for preferences -->
        <div
          v-if="loadingPreferences"
          class="flex items-center justify-center py-10"
        >
          <CoreLoader :loading="loadingPreferences"></CoreLoader>
        </div>

        <div v-else>
          <!-- Results Panel Layout Preference -->
          <div
            class="w-full flex flex-col border border-gray-200 rounded p-4 mb-4"
          >
            <div class="flex justify-between items-start mb-4">
              <div>
                <h3 class="font-bold text-gray-900 text-lg">Results Panel</h3>
                <p class="text-gray-500">
                  Modify default layout when entering results
                </p>
              </div>
            </div>

            <div class="w-full flex flex-col sm:flex-row gap-6">
              <div
                class="w-full sm:w-1/2 cursor-pointer"
                @click="updateLayoutPreference('grid')"
              >
                <div
                  class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
                  :class="{
                    'border-sky-500': layoutPreference === 'grid',
                    'border-gray-100': layoutPreference !== 'grid',
                  }"
                >
                  <div
                    v-if="layoutPreference === 'grid'"
                    class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="48"
                      height="48"
                      viewBox="0 0 48 48"
                      class="w-5 h-5"
                    >
                      <defs>
                        <mask id="ipSCheckOne0">
                          <g
                            fill="none"
                            stroke-linejoin="round"
                            stroke-width="4"
                          >
                            <path
                              fill="#fff"
                              stroke="#fff"
                              d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                            />
                            <path
                              stroke="#000"
                              stroke-linecap="round"
                              d="m16 24l6 6l12-12"
                            />
                          </g>
                        </mask>
                      </defs>
                      <path
                        fill="currentColor"
                        d="M0 0h48v48H0z"
                        mask="url(#ipSCheckOne0)"
                      />
                    </svg>
                  </div>
                  <div class="flex mb-2 space-x-1">
                    <div class="w-2 h-2 rounded-full bg-red-500"></div>
                    <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                  </div>
                  <div class="flex-1 grid grid-cols-3 gap-2">
                    <div
                      v-for="i in 9"
                      :key="i"
                      class="bg-gray-100 rounded flex items-center justify-center"
                    ></div>
                  </div>
                </div>
                <div>
                  <h3 class="text-base py-2.5 font-medium">Grid (Default)</h3>
                </div>
              </div>

              <div
                class="w-full sm:w-1/2 cursor-pointer"
                @click="updateLayoutPreference('list')"
              >
                <div
                  class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
                  :class="{
                    'border-sky-500': layoutPreference === 'list',
                    'border-gray-100': layoutPreference !== 'list',
                  }"
                >
                  <div
                    v-if="layoutPreference === 'list'"
                    class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="48"
                      height="48"
                      viewBox="0 0 48 48"
                      class="w-5 h-5"
                    >
                      <defs>
                        <mask id="ipSCheckOne0">
                          <g
                            fill="none"
                            stroke-linejoin="round"
                            stroke-width="4"
                          >
                            <path
                              fill="#fff"
                              stroke="#fff"
                              d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                            />
                            <path
                              stroke="#000"
                              stroke-linecap="round"
                              d="m16 24l6 6l12-12"
                            />
                          </g>
                        </mask>
                      </defs>
                      <path
                        fill="currentColor"
                        d="M0 0h48v48H0z"
                        mask="url(#ipSCheckOne0)"
                      />
                    </svg>
                  </div>
                  <div class="flex mb-2 space-x-1">
                    <div class="w-2 h-2 rounded-full bg-red-500"></div>
                    <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                  </div>
                  <div class="flex-1 flex flex-col gap-2">
                    <div
                      v-for="i in 3"
                      :key="i"
                      class="bg-gray-100 rounded flex items-center p-1"
                    >
                      <div class="flex-1">
                        <div class="h-3 w-1/4 bg-gray-300 rounded"></div>
                        <div class="h-2 w-3/4 bg-gray-200 rounded mt-1"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="text-base py-2.5 font-medium">ListView</h3>
                </div>
              </div>
            </div>
          </div>

          <!-- Elasticsearch Preference -->
          <div class="w-full border border-gray-200 rounded p-4">
            <div class="flex justify-between items-start mb-4">
              <div>
                <h3 class="font-bold text-gray-900 text-lg">
                  Tests Search Settings
                </h3>
                <p class="text-gray-500">Configure tests search accuracy</p>
              </div>
            </div>

            <div class="flex items-center space-x-4">
              <div class="form-control">
                <label class="flex items-center cursor-pointer space-x-2">
                  <input
                    type="radio"
                    name="elasticsearch"
                    class="radio checked:bg-sky-500"
                    :checked="elasticsearchAccuracy === 'true'"
                    @change="updateElasticsearchPreference('true')"
                  />
                  <span class="label-text"
                    >High Accuracy, requires both first name and last name
                    (Default)</span
                  >
                </label>
              </div>
              <div class="form-control">
                <label class="flex items-center cursor-pointer space-x-2">
                  <input
                    type="radio"
                    name="elasticsearch"
                    class="radio checked:bg-sky-500"
                    :checked="elasticsearchAccuracy === 'false'"
                    @change="updateElasticsearchPreference('false')"
                  />
                  <span class="label-text">Standard Accuracy</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Test Catalog Tab -->
      <div v-if="tab == 3">
        <TestCatalogManagement />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDownTrayIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useFacilityStore } from "@/store/facility";
import type { Preference, Request, Response } from "@/types";
import moment from "moment";
import Package from "@/package.json";
import { useAuthStore } from "@/store/auth";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - Configuration`,
});

type Printer = {
  name: string;
  description: string;
  date_created: string;
  [key: string]: any;
};

type TableHeader = {
  text: string;
  value: string;
};

type PreferenceOption = {
  options: string[];
  default: string;
  description: string;
};

const route = useRoute();
const { can } = usePermissions();

const config = ref<string[]>(["Facility", "Printers", "Preferences", "Test Catalog"]);
const filteredTabs = computed(() => {
  return can.manage("lab_configurations") ? config.value : [config.value[2]];
});

const tab = ref<number>(can.manage("lab_configurations") ? 0 : 2);
const saveIcon = ArrowDownTrayIcon;
const printersHeader = ref<TableHeader[]>([
  { text: "name", value: "name" },
  { text: "description", value: "description" },
  { text: "date created", value: "date_created" },
  { text: "Actions", value: "actions" },
]);
const authStore = useAuthStore();

const systemPreferences = ref<Preference[]>([]);
const layoutPreference = ref<string>("grid");
const elasticsearchAccuracy = ref<string>("true");
const loadingPreferences = ref<boolean>(false);

const printers = ref<Printer[]>([]);
const facility = useFacilityStore();
const { $toast } = useNuxtApp();
const name = ref<string>("");
const code = ref<string>("");
const phone = ref<string>("");
const address = ref<string>("");
const district = ref<string>("");
const loading = ref<boolean>(false);
const loadingPrinters = ref<boolean>(false);

const init = (): void => {
  name.value = facility.details.name;
  phone.value = facility.details.phone;
  code.value = facility.details.code;
  address.value = facility.details.address;
  district.value = facility.details.district;

  if (route.query.tab === "printers") {
    tab.value = 1;
  } else if (route.query.tab === "preferences") {
    tab.value = 2;
  }
};

const loadPrinters = async (): Promise<void> => {
  loadingPrinters.value = true;
  const request: Request = {
    route: endpoints.printers,
    method: "GET",
  };

  const { data, error, pending }: Response = await fetchRequest(request);

  loadingPrinters.value = pending as boolean;

  if (data.value) {
    printers.value = data.value;
    loadingPrinters.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loadingPrinters.value = false;
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: `${endpoints.global}/${facility.details.id}`,
    method: "PUT",
    body: {
      name: name.value,
      code: code.value,
      address: address.value,
      phone: phone.value,
      district: district.value,
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending as boolean;

  if (data.value) {
    $toast.success("Facility updated successfully!");
    emit("update", true);
    globals();
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
};

const globals = async (): Promise<void> => {
  const { fetchFacility } = useFacilityStore();
  const request: Request = {
    route: endpoints.global,
    method: "GET",
  };
  const { data, error }: Response = await fetchRequest(request);
  if (error.value) console.error(error.value);
  if (data.value) fetchFacility(data.value);
};

const fetchSettings = async (): Promise<void> => {
  loadingPreferences.value = true;

  const request: Request = {
    route: endpoints.preferences,
    method: "GET",
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    loadingPreferences.value = false;
    $toast.error("Failed to load preferences");
  }

  if (data.value) {
    systemPreferences.value = data.value;
    data.value.forEach((preference: Preference) => {
      try {
        if (preference.name === "grid_or_list_enter_result_view") {
          const valueObj = JSON.parse(preference.value) as PreferenceOption;
          const userPref = authStore.user.preferences.find(
            (p: Preference) => p.name === preference.name
          );

          layoutPreference.value = userPref?.value || valueObj.default;
        } else if (preference.name === "elasticsearch_enable_high_accuracy") {
          const valueObj = JSON.parse(preference.value) as PreferenceOption;
          const userPref = authStore.user.preferences.find(
            (p: Preference) => p.name === preference.name
          );

          elasticsearchAccuracy.value = userPref?.value || valueObj.default;
        }
      } catch (e) {
        console.error("Error parsing preference:", e);
      }
    });

    loadingPreferences.value = false;
  }
};

const updateLayoutPreference = async (value: string): Promise<void> => {
  if (layoutPreference.value === value) return;

  layoutPreference.value = value;

  const preference = authStore.user.preferences.find(
    (p: Preference) => p.name === "grid_or_list_enter_result_view"
  );

  preference
    ? await updateUserPreference(preference.id, preference.name, value)
    : await createUserPreference("grid_or_list_enter_result_view", value);
};

const updateElasticsearchPreference = async (value: string): Promise<void> => {
  if (elasticsearchAccuracy.value === value) return;
  elasticsearchAccuracy.value = value;

  const preference = authStore.user.preferences.find(
    (p: Preference) => p.name === "elasticsearch_enable_high_accuracy"
  );

  preference
    ? await updateUserPreference(preference.id, preference.name, value)
    : await createUserPreference("elasticsearch_enable_high_accuracy", value);
};

const updateUserPreference = async (
  preferenceId: number,
  name: string,
  value: string
): Promise<void> => {
  const request: Request = {
    route: `${endpoints.userPreferences}/${preferenceId}`,
    method: "PUT",
    body: {
      name: name,
      value: value,
    },
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    $toast.error("Failed to update preference");
  }

  if (data.value) {
    const updatedPreferences = [...authStore.user.preferences];
    const index = updatedPreferences.findIndex((p) => p.id === preferenceId);
    if (index !== -1) {
      updatedPreferences[index] = { ...updatedPreferences[index], value };
      authStore.updateUserPreferences(updatedPreferences);
    }
    $toast.success("Preference updated successfully!");
  }
};

const createUserPreference = async (
  name: string,
  value: string
): Promise<void> => {
  const request: Request = {
    route: endpoints.userPreferences,
    method: "POST",
    body: {
      name: name,
      value: value,
    },
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    $toast.error("Failed to create preference");
  }

  if (data.value) {
    const updatedPreferences = [...authStore.user.preferences, data.value];
    authStore.updateUserPreferences(updatedPreferences);

    $toast.success("Preference created successfully!");
  }
};

const emit = defineEmits<{
  (e: "update", value: boolean): void;
}>();

onMounted(() => {
  init();
  loadPrinters();
  fetchSettings();
});
</script>
