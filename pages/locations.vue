<template>
  <div class="py-20">
    <div
      class="px-10 py-5 flex flex-col items-center rounded shadow max-w-sm mx-auto bg-white"
    >
      <img
        src="@/assets/images/logo.png"
        alt="app-logo"
        class="w-28 h-28 object-cover"
      />

      <div
        class="mt-5 w-full text-3xl font-bold text-sky-500 text-center uppercase"
      >
        {{ appName }}
      </div>

      <h3 class="mt-3 text-2xl font-bold text-black text-center">
        {{ facility.details.name }}
      </h3>
      <div class="w-full flex flex-col space-y-2.5">
        <label class="text-lg font-semibold text-gray-500 text-center"
          >Select Laboratory Location</label
        >
        <div class="w-full grid grid-cols-2 gap-5" v-show="!loading">
          <div
            @click="getLocation(location)"
            v-for="location in locations"
            v-bind:key="location.id"
            class="relative py-10 px-10 flex flex-col items-center bg-gray-50 text-zinc-500 border rounded hover:bg-gray-100 transition duration-150 hover:cursor-pointer"
          >
            <img
              src="@/assets/icons/lab.png"
              class="w-12 h-12 object-cover"
              alt="lab-icon"
            />
            <p class="w-full font-medium text-lg text-center mt-1">
              {{ location.name }}
            </p>
            <div
              v-if="selectedLocation.name == location.name"
              class="absolute top-1.5 left-1.5"
            >
              <CheckCircleIcon class="w-6 h-6 text-green-500" />
            </div>
          </div>
        </div>
      </div>
      <div class="w-full mt-3" v-if="selectedLocation.name !== ''">
        <CoreButton
          text="Continue &rarr;"
          color="primary"
          type="submit"
          @click="navigateHome"
          :loading="loading"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/store/auth";
import { useFacilityStore } from "@/store/facility";
import Package from "@/package.json";
import type { DropdownItem } from "@/types";
import { CheckCircleIcon } from "@heroicons/vue/24/solid/index.js";
import { useRouteStore } from "@/store/route";

const appName = Package.name;
const facility = useFacilityStore();
const router = useRouter();
const authStore = useAuthStore();
const { route } = useRouteStore();
const locations = ref<DropdownItem[]>(authStore.locations);
const loading = ref<boolean>(false);
const selectedLocation = ref<DropdownItem>({ name: "" });
const getLocation = (location: DropdownItem): void => {
  loading.value = true;
  authStore.selectedLocation = location.name;
  selectedLocation.value = location;
  loading.value = false;
};
const navigateHome = () => {
  if (selectedLocation.value.name !== "")
    route == "" ? router.push("/home") : router.push(route);
};
</script>
