<template>
  <div class="w-full">
    <iframe
      ref="iframeRef"
      :src="iframeSrc"
      class="w-full h-screen border-none"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import Package from "@/package.json";
import { useNetworkStore } from "@/store/network";

useHead({
  title: `${Package.name.toUpperCase()} - Usage Manual`,
});

definePageMeta({
  layout: "dashboard",
});

const { ip } = useNetworkStore();
const iframeSrc = `http://${ip}:5173`;
</script>

<style scoped>
.loader {
  text-align: center;
  margin-top: 20px;
}

.error {
  color: red;
  text-align: center;
  margin-top: 20px;
}
</style>
