<template>
  <div class="px-5 py-5">
    <div>
      <CoreBreadcrumb :pages="pages" />

      <div class="flex items-center justify-between py-5">
        <h3 class="text-2xl font-semibold">{{ header }}</h3>
      </div>

      <div
        class="w-full justify-center items-center py-20 mx-auto flex flex-col space-y-2"
        v-if="fetching"
      >
        <CoreLoader />
        <p class="ont-medium text-sky-500">Loading, please wait...</p>
      </div>

      <div v-if="!fetching">
        <div class="grid grid-cols-3 gap-4 py-5">
          <div class="rounded border">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Patient</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Patient Number</h3>
                <p>{{ details.id }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Name</h3>

                <p>
                  {{
                    `${details.client.first_name} ${details.client.middle_name} ${details.client.last_name}`
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Sex</h3>
                <p>{{ details.client.sex }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Age</h3>
                <p>{{ calculateAge(details.client.date_of_birth) }}</p>
              </div>
            </div>
          </div>
          <div class="rounded border">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Specimen</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Specimen Type</h3>
                <p>{{ details.specimen_type }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Tracking Number</h3>
                <p>{{ details.tracking_number }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Accession Number</h3>
                <p>{{ details.accession_number }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Status</h3>
                <p>
                  {{
                    details.order_status
                      .split("-")
                      .join(" ")
                      .charAt(0)
                      .toUpperCase() +
                    details.order_status.split("-").join(" ").slice(1)
                  }}
                </p>
              </div>
            </div>
          </div>
          <div class="rounded border max-h-72 overflow-y-auto">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Test</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Name</h3>
                <p>{{ details.test_type_name }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Date Registered</h3>
                <p>{{ moment(details.created_date).format(DATE_FORMAT) }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Receipt Date</h3>
                <p>{{ moment(details.updated_date).format(DATE_FORMAT) }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Test Status</h3>
                <p>
                  {{
                    details.status.charAt(0).toUpperCase() +
                    details.status.slice(1)
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Ward/Location</h3>
                <p>{{ details.requesting_ward }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Physician</h3>
                <p>
                  {{
                    details.requested_by.charAt(0).toUpperCase() +
                    details.requested_by.slice(1)
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Request Origin</h3>
                <p>{{ details.request_origin }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Registered By</h3>
                <p>{{ details.registered_by }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-5 gap-4">
          <div class="col-span-2 rounded flex flex-col space-y-2.5 border">
            <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
              <h3 class="font-semibold text-gray-700 uppercase">
                Observations
              </h3>
            </div>
            <FormKit
              type="form"
              submit-label="Update"
              @submit="saveResults"
              :actions="false"
            >
              <div
                class="px-5 py-2 space-y-2"
                v-for="(detail, index) in details.indicators"
                :key="index"
              >
                <label class="font-medium">{{ detail.name }}</label>

                <CultureSensitivityOrganisms
                  :result="detail.result"
                  @update="updateResult"
                  @apply="loadOrganisms"
                  :data="detail.indicator_ranges"
                />

                <div>
                  <p class="text-base font-medium mb-2">Remarks</p>
                  <RichTextEditor
                    theme="snow"
                    class="editor"
                    v-model:content="remark"
                    contentType="html"
                  ></RichTextEditor>
                </div>

                <CoreActionButton
                  :loading="saving"
                  :click="() => {}"
                  type="submit"
                  :icon="saveIcon"
                  text="Save changes"
                  color="success"
                />
              </div>
            </FormKit>
          </div>
          <div class="col-span-3 rounded flex flex-col space-y-2.5 border">
            <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
              <h3 class="font-semibold text-gray-700 uppercase">
                Culture worksheet
              </h3>
            </div>
            <div class="px-5 py-2 space-y-2">
              <div>
                <h3 class="mb-2 mt-2 font-medium">Observations & Work-Up</h3>
                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitObservation"
                  :actions="false"
                  #default="{ value }"
                >
                  <table class="w-full text-left border border-dotted">
                    <thead class="bg-gray-50 border-b border-dotted">
                      <tr>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Date
                        </th>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Lab Tech
                        </th>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Remarks
                        </th>
                        <th class="px-2 py-3 font-semibold text-left">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        class="border-b border-dotted"
                        v-for="(
                          observations, index
                        ) in details.culture_observation"
                        :key="index"
                      >
                        <td class="px-2 py-3 border-r border-dotted">
                          {{ moment(observations.observation_date).fromNow() }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          {{
                            `${observations.user.first_name} ${observations.user.middle_name}
                                                ${observations.user.last_name}`
                          }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          <FormKit
                            type="textarea"
                            label=""
                            name="Remarks"
                            :value="observations.description"
                            v-if="observations.description === ''"
                            validation="required"
                          />
                          <p v-if="observations.description != ''">
                            {{ observations.description }}
                          </p>
                        </td>
                        <td class="px-2 py-3">
                          <CoreActionButton
                            type="submit"
                            text="Save"
                            color="success"
                            v-if="observations.description === ''"
                            :icon="saveIcon"
                            :loading="loading"
                            :click="() => {}"
                          />
                        </td>
                      </tr>

                      <tr class="border-b border-dotted">
                        <td class="px-2 py-3 border-r border-dotted">
                          {{ moment().fromNow() }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          {{
                            `${authStore.user.first_name} ${authStore.user.middle_name} ${authStore.user.last_name}`
                          }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          <FormKit
                            type="textarea"
                            label=""
                            name="Remarks"
                            v-model="remarks"
                            validation="required"
                          />
                        </td>
                        <td class="px-2 py-3">
                          <CoreActionButton
                            type="submit"
                            text="Save"
                            color="success"
                            :icon="saveIcon"
                            :loading="loading"
                            :click="() => {}"
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </FormKit>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded border mt-5">
          <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
            <h3 class="font-semibold text-gray-700 uppercase">
              Susceptibility Test Results
            </h3>
          </div>
          <div class="px-5 py-5">
            <div
              v-for="(result, index) in suspceptibilityResult"
              v-bind:key="result.name"
              class="mt-2"
            >
              <div class="flex items-center mb-3 space-x-20">
                <h3 class="font-semibold text-lg">{{ result.name }}</h3>

                <div class="flex items-center space-x-3">
                  <CultureSensitivityDrug
                    :index="index"
                    @update="getUpdatedDrugs"
                  />

                  <CoreActionButton
                    text="Save"
                    color="success"
                    :icon="updateIcon"
                    :loading="updating"
                    :click="() => updateResults(result)"
                  />
                  <CoreActionButton
                    :loading="deleting"
                    :icon="trashIcon"
                    color="error"
                    text="Delete"
                    :click="
                      () => {
                        deleteSusceptivityResult(result.id, index);
                      }
                    "
                  />
                </div>
              </div>
              <table class="text-left border border-dotted">
                <thead class="bg-gray-50 border-b border-dotted">
                  <tr>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Drug
                    </th>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Zone Diameter(mm)
                    </th>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Interpretation (I,S,R)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="border-b border-dotted"
                    v-for="drug in result.drugs"
                    v-bind:key="drug.name"
                  >
                    <td class="px-2 py-3 border-r border-dotted">
                      {{ drug.name }}
                    </td>
                    <td class="px-2 py-3">
                      <CoreDropdown :items="diameters()" v-model="drug.zone" />
                    </td>
                    <td class="px-2 py-3">
                      <CoreDropdown
                        :items="INTERPRETATIONS"
                        v-model="drug.interpretation"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  XMarkIcon,
  ArrowUpTrayIcon,
  TrashIcon,
  CheckIcon,
  PlusIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useAuthStore } from "@/store/auth";
import type { Drug, Page, Request, OrderTest, Response } from "@/types";
import Package from "@/package.json";

export default {
  setup() {
    useHead({
      title: `${Package.name.toUpperCase()} - Enter Culture & Sensitivity Test Results`,
    });
    definePageMeta({
      layout: "dashboard",
    });
  },
  data() {
    return {
      cookie: useCookie("token"),
      authStore: useAuthStore(),
      data: null as unknown as any,
      header: "Enter Culture & Sensitivity Results" as string,
      loading: false as boolean,
      closeIcon: XMarkIcon as Object,
      addIcon: PlusIcon as Object,
      moment: moment,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Tests",
          link: "/tests",
        },
      ] as Page,
      remarks: "" as string,
      remark: "" as string,
      saveIcon: CheckIcon as Object,
      updateIcon: ArrowUpTrayIcon as Object,
      trashIcon: TrashIcon as Object,
      selectedDiameter: { name: "--select result--" } as { name: string },
      details: {
        client: {
          id: 0,
          first_name: "",
          middle_name: "",
          last_name: "",
          sex: "",
          date_of_birth: "",
          birth_date_estimated: false,
        },
        order_status: "",
        status: "",
        requested_by: "",
        culture_observation: new Array<any>(),
        status_trail: new Array<any>(),
        suscept_test_result: new Array<any>(),
      } as OrderTest,
      organisms: new Array<{ id: number; name: string; drugs: Array<Drug> }>(),
      drugsValues: {
        id: 0,
        name: "",
        drugs: new Array<Drug>(),
      } as {
        id: number;
        name: string;
        drugs: Array<Drug>;
      },
      suspceptibilityResult: new Array<any>(),
      updating: false as boolean,
      saving: false as boolean,
      deleting: false as boolean,
      results: {} as any,
      testId: "" as string,
      accessionNo: "" as string,
      fetching: false as boolean,
    };
  },
  created() {
    this.accessionNo = `${this.$route.query.accession_number}`;
    this.testId = `${this.$route.query.test_id}`;
    this.init();
  },
  methods: {
    async init(): Promise<void> {
      this.fetching = true;
      const request: Request = {
        route: `${endpoints.tests}/${this.testId}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.fetching = pending;

      if (data.value) {
        this.details = data.value;
        this.fetching = false;
        this.suspceptibilityResult = data.value.suscept_test_result.map(
          (test: any) => ({
            id: test.organism_id,
            name: test.name,
            drugs: test.drugs.map((drug: any) => ({
              name: drug.name,
              drug_id: drug.drug_id,
              zone: { name: drug.zone === null ? "--select result--" : drug.zone },
              interpretation: {
                name:
                  drug.interpretation === "" || null
                    ? "--select result--"
                    : drug.interpretation,
              },
            })),
          })
        );
        this.remark =
          data.value.result_remarks?.value ?? data.value.result_remarks?.value;
      }

      if (error.value) {
        this.fetching = false;
        console.error("error fetcing test details: ", error.value);
      }
    },
    async loadOrganisms(
      value: Array<{ id: number; name: string; drugs: Array<any> }>
    ): Promise<void> {
      value.forEach(async (element) => {
        const request: Request = {
          route: `${endpoints.organisms}/${element.id}`,
          method: "GET",
          token: `${this.cookie}`,
        };
        const { data, error }: Response = await fetchRequest(request);
        if (data.value) {
          this.organisms.push(data.value);

          let dataHolder: {
            id: number | null;
            name: string | null;
            drugs: Drug[];
          } = {
            id: null,
            name: null,
            drugs: [],
          };

          this.organisms.forEach((organism) => {
            dataHolder.id = organism.id;
            dataHolder.name = organism.name;

            // concatenate the current organism's drugs to the dataHolder's drugs
            const organismDrugs = organism.drugs.map((drug) => ({
              name: drug.name,
              drug_id: drug.id,
              zone: { name: "--select result--" },
              interpretation: { name: "--select result--" },
            }));
            dataHolder.drugs = dataHolder.drugs.concat(organismDrugs);
          });
          this.suspceptibilityResult.push(dataHolder);
        }

        if (error.value) {
          console.error(error.value);
        }
      });
    },
    diameters(): Array<{ name: string }> {
      let diameters = new Array<{ name: string }>();
      for (let i = 0; i <= 200; i++) {
        diameters.push({ name: `${i}` });
      }
      diameters.unshift({name: '--select result--'});
      return diameters;
    },
    async submitObservation(): Promise<void> {
      this.loading = true;
      const request: Request = {
        route: endpoints.cultureObservations,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          test_id: this.testId,
          description: this.remarks,
        },
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.remarks = "";
        this.loading = false;
        this.init();
        this.suspceptibilityResult = new Array<any>();
      }

      if (error.value) {
        this.loading = false;
        console.error(error.value);
      }
    },
    /**
     * @method updateResults load results to backend (new and update data)
     * @returns promise @type void
     * @param null
     */
    async updateResults(details: any): Promise<void> {
      this.updating = true;
      let suspceptibilityResult = this.suspceptibilityResult.filter(
        (organism) => details.name.toLowerCase() === organism.name.toLowerCase()
      );
      suspceptibilityResult.map(async (organism) => {
        let filteredDrugs: any[] = [];
        organism.drugs.map((drug: any) => {
          filteredDrugs.push({
            drug_id: drug.drug_id,
            zone: drug.zone.name !== "--select result--" ? drug.zone.name : "",
            interpretation:
              drug.interpretation.name !== "--select result--"
                ? drug.interpretation.name
                : "",
          });
        });

        const request: Request = {
          route: endpoints.drugSusceptibility,
          method: "POST",
          token: `${this.cookie}`,
          body: {
            test_id: this.details.id,
            organism_id: organism.id,
            drugs: filteredDrugs.filter(
              (drug: Drug) =>
                String(drug.zone) !== "" && String(drug.interpretation) !== ""
            ),
            status: "completed",
          },
        };

        const { data, error, pending }: Response = await fetchRequest(request);
        this.updating = pending;
        if (data.value) {
          this.updating = false;
          useNuxtApp().$toast.success(
            "Suspceptibility test results updated successfully!"
          );
        }
        if (error.value) {
          console.error(error.value);
          this.updating = false;
          useNuxtApp().$toast.error(ERROR_MESSAGE);
        }
      });
      this.updating = false;
    },
    async deleteSusceptivityResult(
      organismId: string,
      index: number
    ): Promise<void> {
      this.deleting = true;
      const request: Request = {
        route: `${endpoints.drugSusceptibility}/delete`,
        method: "PUT",
        token: `${this.cookie}`,
        body: {
          test_id: this.details.id,
          organism_id: organismId,
        },
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.deleting = pending;

      if (data.value) {
        this.deleting = false;
        this.suspceptibilityResult.splice(index, 1);
        useNuxtApp().$toast.success(
          "Suspceptibility test results deleted successfully!"
        );
      }

      if (error.value) {
        console.error(error.value);
        this.deleting = false;
        useNuxtApp().$toast.success("An error has occurred, please try again!");
      }
    },
    invalidation(): void {
      useNuxtApp().$toast.warning(
        "Could not save null suspceptibility results, please enter values!"
      );
      this.updating = false;
    },
    updateResult(value: Object): void {
      this.results = value;
    },
    async saveResults(): Promise<void> {
      this.saving = true;
      const request: Request = {
        route: `${endpoints.updateResults}`,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          test_id: this.details.id,
          test_indicators: this.results?.value == null ? [] : new Array({
            indicator: this.results?.test_indicator_id,
            value: this.results?.value,
          }),
          remarks: this.remark,
        },
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.saving = pending;
      if (data.value) {
        this.saving = false;
        useNuxtApp().$toast.success("Test results updated successfully");
        this.$router.back();
      }

      if (error.value) {
        this.saving = false;
        useNuxtApp().$toast.error(ERROR_MESSAGE);
        console.error(error.value);
      }
    },
    getUpdatedDrugs(value: any): void {
      this.suspceptibilityResult.map((item, index) => {
        if (value.index === index) {
          value.drugs.map((drug: { name: any; id: any }) => {
            item.drugs.push({
              name: drug.name,
              drug_id: drug.id,
              zone: { name: "--select result--" },
              interpretation: { name: "--select result--" },
            });
          });
        }
      });
    },
  },
};
</script>

<style></style>
