<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/ui_folder.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>
      <div class="flex items-center space-x-3">
        <TestPanelsAddDialog
          @update="updateTestPanels"
          v-if="usePermissions().can.manage('test_catalog')"
        />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="testPanels"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <TestPanelsViewDialog :data="item" />
          <TestPanelsEditDialog :data="item" @update="updateTestPanels" />
          <TestPanelsDeleteDialog :data="item" @update="updateTestPanels" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Header, Request, Response } from "@/types";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Test Panels`,
    });
  },
  components: { MagnifyingGlassIcon },
  data() {
    return {
      header: "Test Panels" as string,
      testPanels: new Array<any>(),
      loading: false as boolean,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      search: "" as string,
      searchValue: "" as string,
      cookie: useCookie("token"),
      headers: [
        { text: "id", value: "id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "short name", value: "short_name" },
        { text: "description", value: "description", sortable: true },
        { text: "actions", value: "actions" },
      ] as Header,
    };
  },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    async init(): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: endpoints.testPanels,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.testPanels = data.value;
        this.loading = false;
      }

      if (error.value) {
        console.error(error.value);
        this.loading = false;
      }
    },
    updateTestPanels(value: boolean): void {
      if (value) {
        this.init();
      }
    },
  },
};
</script>

<style></style>
