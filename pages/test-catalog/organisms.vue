<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/bacteria.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>

      <div class="flex items-center space-x-3">
        <OrganismsAddDialog
          @update="updateOrganisms"
          v-if="usePermissions().can.manage('test_catalog')"
        />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar :search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="organisms"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <OrganismsViewDialog :data="item" />
          <OrganismsEditDialog :data="item" @update="updateOrganisms" />
          <OrganismsDeleteDialog :data="item" @update="updateOrganisms" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Header, Page } from "@/types";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Organisms`,
    });
  },
  data() {
    return {
      header: "Organisms" as string,
      loading: false as boolean,
      search: "" as string,
      searchValue: "" as string,
      headers: [
        { text: "id", value: "id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "description", value: "description" },
        { text: "actions", value: "actions" },
      ] as Header,
      organisms: [],
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      cookie: useCookie("token"),
    };
  },
  components: { MagnifyingGlassIcon },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    async init() {
      this.loading = true;

      const request: Request = {
        route: endpoints.organisms,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.organisms = data.value;
        this.loading = false;
      }

      if (error.value) {
        console.error(error.value);
        this.loading = false;
      }
    },
    updateOrganisms(value: boolean): void {
      if (value) {
        this.init();
      }
    },
  },
};
</script>

<style></style>
