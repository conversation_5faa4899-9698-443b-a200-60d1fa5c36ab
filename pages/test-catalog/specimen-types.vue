<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/medical_sample.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>

      <div class="flex items-center space-x-3">
        <SpecimenTypesAddDialog @update="updateSpecimenTypes" v-if="usePermissions().can.manage('test_catalog')"/>
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="specimens"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <SpecimenTypesViewDialog :data="item" />
          <SpecimenTypesEditDialog :data="item" @update="updateSpecimenTypes" />
          <SpecimenTypesDeleteDialog
            :data="item"
            @update="updateSpecimenTypes"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid/index.js";
import type { Header, Page, Request, Response } from "@/types";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Specimen Types`,
    });
  },
  components: { MagnifyingGlassIcon },
  data() {
    return {
      header: "Specimen Types",
      specimens: new Array<Object>(),
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      loading: false as boolean,
      search: "" as string,
      searchValue: "" as string,
      cookie: useCookie("token"),
      headers: [
        { text: "id", value: "id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "description", value: "description", sortable: true },
        { text: "actions", value: "actions" },
      ] as Header,
    };
  },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    async init(): Promise<void> {
      this.loading = true;
      const request: Request = {
        route: endpoints.specimens,
        method: "GET",
        token: `${this.cookie}`,
        body: {},
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.specimens = data.value;
      }

      if (error.value) {
        console.error(error.value);
      }
    },
    updateSpecimenTypes(value: boolean): void {
      if (value) {
        this.init();
      }
    },
  },
};
</script>

<style></style>
