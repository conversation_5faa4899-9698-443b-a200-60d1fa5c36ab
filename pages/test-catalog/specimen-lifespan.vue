<script setup lang="ts">
import type { Header, Item, ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Request, Response } from "@/types";
import { getParameterizedUrl } from "@/utils/functions";
import Package from "@/package.json";

useSeoMeta({
  title: `${Package.name.toUpperCase()} - Specimen Lifespan`,
});

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

const serverItemsLength: Ref<number> = ref<number>(0);
const loading: Ref<boolean> = ref<boolean>(false);
const specimenLifespans: Ref<Item[]> = ref<Item[]>([]);

const cookie: Ref<string | null> = useCookie("token");
const header: Ref<string> = ref<string>("Specimen Lifespan");
const search: Ref<string> = ref<string>("");

const serverOptions: Ref<ServerOptions> = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 10,
  sortBy: "name",
});

const pages: Ref<Page> = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
]);

const headers: Ref<Header[]> = ref<Header[]>([
  { text: "Specimen Type", value: "specimen_name", sortable: true },
  { text: "Test Type", value: "test_type_name" },
  { text: "Lifesapn", value: "specimen_life_span" },
  { text: "Actions", value: "actions" },
]);

type TSpecimenLifespan = {
  id: any;
  specimen_name: any;
  test_type_name: any;
  life_span: any;
  life_span_units: any;
};

const loadSpecimenLifespans = async (): Promise<void> => {
  loading.value = true;
  const { page, rowsPerPage } = serverOptions.value;

  const request: Request = {
    route: getParameterizedUrl(endpoints.specimensLifespan.index, {
      page: page,
      per_page: rowsPerPage,
      search: search.value,
    }),
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    const rows = data.value.data;
    specimenLifespans.value = rows
      ? rows.map((row: TSpecimenLifespan) => ({
          id: row.id,
          specimen_name: row.specimen_name,
          test_type_name: row.test_type_name,
          specimen_life_span:
            row.life_span && row.life_span_units
              ? `${row.life_span} ${row.life_span_units}`
              : "--",
          life_span: row.life_span,
          life_span_unit: row.life_span_units,
        }))
      : [];
    serverItemsLength.value = data.value.meta.total_count ?? 0;
    loading.value = false;
  }

  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
  }
};


const updateServerOptions = (options: ServerOptions): ServerOptions => (serverOptions.value = options);

onMounted(() => {
  loadSpecimenLifespans();
})

watch(serverOptions, () => loadSpecimenLifespans());

watch(search, () => loadSpecimenLifespans());

const reloadItems = async (): Promise<void> => {
  loadSpecimenLifespans();
};
</script>

<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="text-2xl font-semibold flex items-center uppercase py-5">
      <img
        src="@/assets/icons/medical_sample.svg"
        alt="report-icon"
        class="w-8 h-8 mr-2"
      />
      {{ header }}
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        v-if="usePermissions().can.manage('test_catalog')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="specimenLifespans"
      :serverOptions="serverOptions"
      :loading="loading"
      :serverItemsLength="serverItemsLength"
      @update="updateServerOptions"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <SpecimenLifespanEditDialog
            @action-completed="reloadItems"
            :id="item.id"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>
