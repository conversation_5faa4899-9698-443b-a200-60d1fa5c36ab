<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/ui_folder.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>

      <div class="flex items-center space-x-3">
        <CoreActionButton
          text="Create Test Type"
          :icon="addIcon"
          color="primary"
          :click="createTestType"
          v-if="usePermissions().can.manage('test_catalog')"
        />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        :search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('test_catalog')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="testTypes"
      :loading="loading"
      :searchField="searchField"
      :searchValue="searchValue"
      :serverItemsLength="serverItemsLength"
      :serverOptions="serverOptions"
      @update="updateTestTypes"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }" :loading="loading">
        <div class="py-2 flex items-center space-x-2">
          <TestTypesViewDialog @edit="edit" :open="showEdit" :data="item" />
          <CoreActionButton
            :click="
              () => {
                navigateToEdit(item);
              }
            "
            color="success"
            text="Edit"
            :icon="editIcon"
          />
          <TestTypesDeleteDialog @update="updateTestTypes" :data="item" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import {
  MagnifyingGlassIcon,
  PencilSquareIcon,
  PlusIcon,
} from "@heroicons/vue/24/solid/index.js";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Header, Request, Page } from "@/types";
import Package from "@/package.json";

export default {
  components: {
    MagnifyingGlassIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Test Types`,
    });
  },
  data() {
    return {
      header: "Test Types" as string,
      loading: true as boolean,
      addIcon: PlusIcon,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      editIcon: PencilSquareIcon,
      showEdit: false as boolean,
      serverItemsLength: 0 as number,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "name",
      },
      search: "" as string,
      testTypes: new Array<any>(),
      cookie: useCookie("token"),
      headers: [
        { text: "name", value: "name", sortable: true },
        { text: "short name", value: "short_name" },
        {
          text: "expected turn around time",
          value: "expected_turn_around_time",
        },
        { text: "actions", value: "actions" },
      ] as Header,
      searchField: "name" as string,
      searchValue: "" as string,
    };
  },
  created() {
    this.init();
    if (Boolean(this.$route.query.create))
      useNuxtApp().$toast.success("Test type created successfully!");
  },
  methods: {
    async init(): Promise<void> {
      const { page, rowsPerPage } = this.serverOptions;
      this.loading = true;
      const request: Request = {
        route: `${endpoints.testTypes}?page=${page}&per_page=${rowsPerPage}&search=${this.search}`,
        method: "GET",
        token: `${this.cookie}`,
        body: {},
      };
      let { data, error, pending } = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.testTypes = data.value.test_types.map(
          (testType: {
            expected_turn_around_time: { value: any; unit: string };
          }) => ({
            ...testType,
            expected_turn_around_time: `${
              testType.expected_turn_around_time !== null
                ? testType.expected_turn_around_time?.value +
                  " " +
                  (testType.expected_turn_around_time.unit == null
                    ? ""
                    : testType.expected_turn_around_time.unit?.toLowerCase())
                : ""
            }`,
          })
        );
        this.serverItemsLength = data.value.meta.total_count;
      }
      if (error.value) {
        console.error(error.value);
      }
    },
    updateTestTypes(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
      this.updateTestTypes(true);
    },
    edit(value: boolean): void {
      this.showEdit = true;
    },
    navigateToEdit(item: { id: number; name: string }): void {
      const slug = item.name
        .trim()
        .toLowerCase()
        .replace(/[\s\W-]+/g, "-")
        .replace(/^-+|-+$/g, "");
      this.$router.push(
        `/test-catalog/test-types/edit/${slug}?testType=${item.id}`
      );
    },
    createTestType(): void {
      this.$router.push("/test-catalog/test-types/create");
    },
  },
};
</script>

<style></style>
