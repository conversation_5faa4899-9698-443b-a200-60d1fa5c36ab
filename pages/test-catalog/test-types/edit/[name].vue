<template>
  <div class="px-5 py-5">
    <div v-if="loading" class="w-full flex flex-col space-y-2 items-center justify-center py-20">
      <CoreLoader/>
      <label>Loading test type data, please wait...</label>
    </div>
    <div v-else>
      <CoreBreadcrumb :pages="pages" />
      <div class="flex items-center justify-between py-5">
        <h3 class="text-2xl font-semibold">{{ details.name }}</h3>
      </div>
      <div>
        <FormKit
          type="form"
          submit-label="Update"
          @submit="submitForm"
          :actions="false"
          #default="{ value }"
        >
          <div class="mt-2 space-y-3">
            <div class="grid grid-cols-2 gap-4">
              <FormKit
                type="text"
                label="Name"
                validation="required"
                v-model="details.name"
              />
              <FormKit
                type="text"
                label="Short name"
                validation="required"
                v-model="details.short_name"
              />
            </div>

            <div class="w-full flex">
              <FormKit
                type="number"
                label="Expected Turn Around Time"
                validation="required|number"
                v-model="expected_turn_around_time"
              />
              <div class="py-1 ml-2">
                <label class="font-medium text-base">Duration</label>
                <CoreDropdown :items="timeTypes" v-model="duration" />
              </div>
            </div>

            <div class="w-full flex items-center space-x-2">
              <input
                id="showCultureWorksheet"
                name="showCultureWorksheet"
                type="checkbox"
                v-model="showCultureWorksheet"
              />
              <label for="showCultureWorksheet">Show Culture Worksheet?</label>
            </div>

            <div
              v-show="showCultureWorksheet"
              class="w-full flex flex-col space-y-2"
            >
              <label class="font-medium">Organisms</label>
              <multi-select
                style="--ms-max-height: none !important"
                v-model="organismsSelected"
                :options="organisms"
                mode="tags"
                clear
                searchable
                class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
              />
            </div>

            <div class="w-full flex flex-col space-y-2">
              <label class="font-medium">Specimens</label>
              <multi-select
                style="--ms-max-height: none !important"
                v-model="specimensSelected"
                :options="specimens"
                mode="tags"
                required
                clear
                class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
              />
            </div>

            <div class="w-full flex items-center space-x-2">
              <input
                id="shouldPrintResults"
                name="shouldPrintResults"
                type="checkbox"
                v-model="shouldPrintResults"
              />
              <label for="shouldPrintResults"
                >Print Results On Small Label?</label
              >
            </div>

            <div class="w-full flex items-center">
              <div class="w-full flex flex-col space-y-2">
                <label class="font-medium">Lab section</label>
                <CoreDropdown
                  :items="departments"
                  v-model="details.department"
                />
              </div>
            </div>

            <CoreMultiselect
              label="Specific Sex"
              v-model:items-selected="details.sex"
              :items="GENDER.map((gender) => gender.name)"
              mode="single"
            />

            <div class="w-full flex">
              <CoreActionButton
                type="button"
                :click="
                  () => {
                    addMeasure(details.indicators);
                  }
                "
                color="primary"
                text="Add Measure"
                :icon="addIcon"
              />
            </div>

            <div
              v-if="details.indicators.length != 0"
              v-for="(indicator, index) in details.indicators"
              :key="index"
              class="border py-3 px-3 rounded"
            >
              <div class="py-3 flex items-center justify-between">
                <h3 class="text-lg text-grameasuresy-700 font-semibold">
                  Measures ({{ index + 1 }})
                </h3>
                <button
                  type="button"
                  @click="
                    () => {
                      details.indicators.splice(index, 1);
                    }
                  "
                >
                  <XMarkIcon class="w-5 h-5" />
                </button>
              </div>
              <div class="grid grid-cols-4 gap-2 mb-3">
                <FormKit
                  type="text"
                  label="Name"
                  validation="required"
                  v-model="indicator.name"
                />

                <div class="mt-1">
                  <label class="font-medium">Type</label>
                  <CoreDropdown
                    :items="measureTypes"
                    v-model="indicator.test_indicator_type"
                  />
                </div>

                <FormKit type="text" label="Unit" v-model="indicator.unit" />
                <FormKit
                  type="text"
                  label="Description"
                  v-model="indicator.description"
                />
              </div>

              <div v-if="indicator.test_indicator_type.name != 'Free Text'">
                <CoreActionButton
                  type="button"
                  :click="
                    () => {
                      addRange(index, indicator);
                    }
                  "
                  color="primary"
                  text="Add New Range"
                  :icon="addIcon"
                />
              </div>

              <div
                class="px-3 py-3"
                v-if="
                  indicator.indicator_ranges.length > 0 &&
                  indicator.test_indicator_type.name === 'Numeric'
                "
                v-for="(measure, index) in indicator.indicator_ranges"
                :key="index"
              >
                <div class="flex items-center space-x-3">
                  <div class="grid grid-cols-2 gap-2">
                    <div class="">
                      <label class="mb-2 text-lg font-semibold text-gray-600"
                        >Age Range</label
                      >
                      <div class="grid grid-cols-2 gap-2">
                        <FormKit
                          type="number"
                          label="Minimum"
                          validation="required"
                          v-model="measure.min_age"
                        />
                        <FormKit
                          type="number"
                          label="Maximum"
                          validation="required"
                          v-model="measure.max_age"
                        />

                        <div>
                          <label class="-mb-2 font-semibold">Sex</label>

                          <select
                            class="w-full bg-white border px-2 py-2 focus:ring-none rounded"
                            v-model="measure.sex"
                          >
                            <option
                              v-for="(item, index) in patientSex"
                              :key="index"
                              :value="item.name"
                            >
                              {{ item.name }}
                            </option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="">
                      <label class="mb-2 text-lg font-semibold text-gray-600"
                        >Measure Range</label
                      >
                      <div class="grid grid-cols-2 gap-2">
                        <FormKit
                          type="number"
                          step="any"
                          number
                          label="Minimum"
                          validation="required|float"
                          v-model="measure.lower_range"
                        />
                        <FormKit
                          type="number"
                          step="any"
                          number
                          label="Maximum"
                          validation="required|float"
                          v-model="measure.upper_range"
                        />
                      </div>
                    </div>
                    <FormKit
                      type="text"
                      label="Interpretation"
                      v-model="measure.interpretation"
                    />
                  </div>
                  <div class="-mt-20">
                    <CoreActionButton
                      type="button"
                      color="error"
                      text="Remove"
                      :icon="trashIcon"
                      :click="
                        () => {
                          indicator.indicator_ranges.splice(index, 1);
                        }
                      "
                    />
                  </div>
                </div>
              </div>

              <div
                class="px-3 py-3"
                v-if="
                  indicator.indicator_ranges.length != 0 &&
                  indicator.test_indicator_type.name === 'Auto Complete'
                "
                v-for="(measure, index) in indicator.indicator_ranges"
                :key="index"
              >
                <div class="w-full items-center flex space-x-3">
                  <div class="flex items-center gap-2">
                    <FormKit
                      type="text"
                      label="Value"
                      validation="required"
                      v-model="measure.value"
                    />
                    <FormKit
                      type="text"
                      label="Interpretation"
                      v-model="measure.interpretation"
                    />
                  </div>
                  <div class="mt-8">
                    <CoreActionButton
                      type="button"
                      color="error"
                      text="Remove"
                      :icon="trashIcon"
                      :click="
                        () => {
                          indicator.indicator_ranges.splice(index, 1);
                        }
                      "
                    />
                  </div>
                </div>
              </div>

              <div
                class="flex items-center px-3 py-3 rounded bg-gray-50 mt-3"
                v-if="
                  ranges != 0 &&
                  indicator.test_indicator_type.name === 'Free Text'
                "
                v-for="(measure, index) in ranges"
                :key="index"
              >
                <InformationCircleIcon class="w-5 h-5 mr-2" /> A text box will
                appear for results entry
              </div>
            </div>
          </div>

          <div class="justify-end flex items-center space-x-3 px-3 py-2">
            <CoreActionButton
              type="submit"
              :click="() => {}"
              :loading="updating"
              color="success"
              :icon="saveIcon"
              text="Save Changes"
            />
          </div>
        </FormKit>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from "@headlessui/vue";
import {
  PlusIcon,
  XMarkIcon,
  UserIcon,
  TrashIcon,
  CheckCircleIcon,
  ArrowDownTrayIcon,
  ArrowUturnLeftIcon,
  InformationCircleIcon,
  ChevronUpDownIcon,
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type {
  Autocomplete,
  Department,
  Indicator,
  IndicatorRange,
  TestIndicatorType,
  TestType,
  Page,
  Request,
  Response,
} from "@/types";

import Package from "@/package.json";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
    XMarkIcon,
    UserIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    ChevronUpDownIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Test Types`,
    });
  },
  data() {
    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      removeIcon: XMarkIcon,
      trashIcon: TrashIcon,
      loadingSpecimens: false as boolean,
      showMeasures: false as boolean,
      measures: 1 as number,
      ranges: 0 as number,
      indicators: [
        {
          name: "",
          test_indicator_type: 0,
          unit: "",
          description: "",
          indicator_ranges: [] as Array<IndicatorRange | Autocomplete>,
        },
      ] as Array<Indicator>,
      numericRange: {
        max_age: 0,
        min_age: 0,
        lower_range: 0,
        upper_range: 0,
        interpretation: "",
      } as IndicatorRange,
      autocompleteRange: { value: "", interpretation: "" } as Autocomplete,
      name: "" as string,
      sexSelected: <string>"",
      shortName: "" as string,
      turnAroundTime: "" as string,
      measureTypes: Array<TestIndicatorType>(),
      measureSelected: { name: "", id: 0 },
      loading: false as boolean,
      updating: false as boolean,
      editIcon: PencilSquareIcon,
      id: this.$route.query.testType,
      patientSex: new Array(
        { name: "Male", label: "Male", value: "M" },
        { name: "Female", label: "Female", value: "F" },
        { name: "Both", label: "Both", value: "Both" }
      ),
      details: {
        name: "",
        short_name: "",
        expected_turn_around_time: {
          value: "",
          unit: "",
        },
        test_indicator_type: {
          id: 0,
          name: "",
        },
        department: {
          id: 0,
          name: "",
        },
        specimens: {
          id: 0,
          name: "",
          created_date: "",
        },
        indicators: new Array<any>(),
        organisms: new Array<any>(),
      } as TestType | any,
      specimensSelected: new Array<string>(),
      rawOrganisms: new Array<{}>(),
      organisms: new Array<string>(),
      organismsSelected: new Array<String>(),
      shouldPrintResults: false as boolean,
      showCultureWorksheet: false as boolean,
      timeTypes: new Array(
        { name: "Month" },
        { name: "Weeks" },
        { name: "Days" },
        { name: "Hours" },
        { name: "Minutes" }
      ),
      duration: { name: "-- select value --" },
      expected_turn_around_time: "" as string,
      cookie: useCookie("token"),
      rawSpecimens: new Array<String>(),
      specimens: new Array<String>(),
      departments: new Array<Department>(),
      measureType: "" as string,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
        {
          name: "Test Types",
          link: "/test-catalog/test-types",
        },
      ] as Page,
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;
      await this.loadSpecimens();
      await this.loadDepartments();
      await this.loadTestTypeIndicators();
      await this.loadOrganisms();
      const request: Request = {
        route: `${endpoints.viewTestType}/${this.$route.query.testType}`,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.details = data.value;
        this.specimensSelected = reverseFilterArrays(data.value.specimens);
        this.organismsSelected = reverseFilterArrays(data.value.organisms);
        this.showCultureWorksheet = data.value.organisms.length > 0;
        this.shouldPrintResults = data.value.print_device;
        this.duration = {
          name:
            data.value.expected_turn_around_time?.unit !== null
              ? data.value.expected_turn_around_time?.unit
              : "-- select value --",
        };
        this.expected_turn_around_time =
          data.value.expected_turn_around_time?.value;
        this.sexSelected = data.value.sex;
        this.loading = false;
      }

      if (error.value) {
        console.error("error:", error.value);
        this.loading = false;
      }
    },
    async loadOrganisms() {
      const request: Request = {
        route: endpoints.organisms,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { error, data }: Response = await fetchRequest(request);
      if (data.value) {
        this.rawOrganisms = data.value;
        data.value.map((organism: { name: string }) => {
          this.organisms.push(organism.name);
        });
      }
      if (error.value) {
        console.log(error.value);
      }
    },
    async loadSpecimens(): Promise<void> {
      const request: Request = {
        route: endpoints.specimens,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, pending, error }: Response = await fetchRequest(request);
      this.loadingSpecimens = pending;
      if (data.value) {
        this.loadingSpecimens = false;
        this.rawSpecimens = data.value;
        data.value.map((specimen: { name: string }) => {
          this.specimens.push(specimen.name);
        });
      }
      if (error.value) {
        this.loadingSpecimens = false;
        console.error(error.value);
      }
    },
    async loadDepartments() {
      const request: Request = {
        route: endpoints.departments,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error }: Response = await fetchRequest(request);
      if (data.value) {
        this.departments = data.value;
      }
      if (error.value) {
        console.error("error:", error.value);
      }
    },
    async loadTestTypeIndicators() {
      const request: Request = {
        route: `${endpoints.testTypes}/test_indicator_types`,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error }: Response = await fetchRequest(request);
      if (data.value) {
        this.measureTypes = data.value;
        this.measureSelected = data.value[0];
        this.measureType = this.measureSelected.name;
      }
      if (error.value) {
        console.error(error.value);
      }
    },
    addMeasure(indicators: Array<Object>): any {
      indicators.push({
        name: "" as string,
        test_indicator_type: this.measureSelected,
        unit: "" as string,
        description: "" as string,
        indicator_ranges: [] as Array<any>,
      });
    },
    addRange(_index: number, indicators: any) {
      this.ranges = this.ranges + 1;
      if (this.measureSelected.name == "Numeric") {
        try {
          indicators.indicator_ranges.push(this.numericRange);
        } catch (error) {
          console.error("error:", error);
        }
      } else if (this.measureSelected.name === "Auto Complete") {
        try {
          indicators.indicator_ranges.push({
            value: "",
            interpretation: "",
          });
        } catch (error) {
          console.error("error:", error);
        }
      }
    },
    filterSpecimens(raw: Array<any>, items: Array<String>): Array<Number> {
      let filteredSpecimens = new Array<Number>();
      items.map((name) => {
        const obj = raw.filter((obj) => obj.name === name);
        filteredSpecimens.push(obj[0].id);
      });
      return filteredSpecimens;
    },

    async submitForm(): Promise<void> {
      this.updating = true;
      this.details.specimens = this.filterSpecimens(
        this.rawSpecimens,
        this.specimensSelected
      );
      this.details.department = this.details.department.id;
      this.details.organisms = this.showCultureWorksheet
        ? filterArrays(this.rawOrganisms, this.organismsSelected)
        : new Array();
      this.details.expected_turn_around_time = {
        value: this.expected_turn_around_time,
        unit: this.duration.name,
      };
      this.details.print_device = this.shouldPrintResults;
      const request: Request = {
        route: `${endpoints.testTypes}/${this.$route.query.testType}`,
        method: "PUT",
        token: `${this.cookie}`,
        body: this.details,
      };
      const { pending, data, error }: Response = await fetchRequest(request);
      this.updating = pending;
      if (data.value) {
        this.open = false;
        useNuxtApp().$toast.success(`Test type updated successfully!`);
        this.updating = false;
        this.$emit("update", true);
      }
      if (error.value) {
        useNuxtApp().$toast.error(`An error occurred, please try again!`);
        console.error("error:", error.value);
        this.updating = false;
      }
    },
    removeObjectKeys(measure: string, key: string): void {
      if (measure == "rich_text" || measure == "free_text") {
        delete this.details[key];
      }
    },
  },
};
</script>

<style></style>
