<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />
    <div class="flex items-center justify-between">
      <h3 class="text-2xl font-semibold mt-5">Create Test Type</h3>
    </div>
    <div>
      <div
        v-show="loading"
        class="flex items-center justify-center mx-auto my-20"
      >
        <CoreLoader :loading="loading" />
      </div>

      <div v-show="!loading">
        <FormKit
          id="submitForm"
          type="form"
          submit-label="Update"
          @submit="validator() && submitForm()"
          :actions="false"
          #default="{ value }"
        >
          <div class="mt-2 space-y-3">
            <div class="grid grid-cols-2 gap-4">
              <FormKit
                type="text"
                label="Name"
                validation="required"
                v-model="name"
              />

              <FormKit
                type="text"
                label="Short name"
                validation="required"
                v-model="shortName"
              />
            </div>

            <div class="w-full flex items-center">
              <FormKit
                type="number"
                label="Expected Turn Around Time"
                validation="required|number"
                v-model="turnAroundTime"
              />
              <div class="py-1 ml-2 flex flex-col space-y-2">
                <p class="font-medium text-base">Duration</p>
                <CoreDropdown :items="durations" v-model="duration" />
              </div>
            </div>

            <div class="w-full flex items-center space-x-2">
              <input
                id="showCultureWorksheet"
                name="showCultureWorksheet"
                type="checkbox"
                v-model="showCultureWorksheet"
              />
              <label for="showCultureWorksheet">Show Culture Worksheet?</label>
            </div>

            <div
              v-show="showCultureWorksheet"
              class="w-full flex flex-col space-y-2"
            >
              <label class="font-medium">Organisms</label>
              <multi-select
                style="--ms-max-height: none !important"
                v-model="organismsSelected"
                :options="organisms"
                mode="tags"
                :required="false"
                clear
                searchable
                class="focus:ring-none focus:border-none focus:outline-none multiselect-green"
              />
            </div>

            <div class="w-full flex flex-col space-y-2">
              <label class="font-medium">Specimens</label>
              <multi-select
                style="--ms-max-height: none !important"
                v-model="specimensSelected"
                :options="specimens"
                mode="tags"
                required
                searchable
                clear
                class="focus:ring-none focus:border-none focus:outline-none multiselect-green"
              />
            </div>

            <div class="w-full flex items-center space-x-2">
              <input
                id="shouldPrintResults"
                name="shouldPrintResults"
                type="checkbox"
                v-model="shouldPrintResults"
              />
              <label for="shouldPrintResults"
                >Print Results On Small Label?</label
              >
            </div>

            <div class="w-full flex flex-col space-y-2">
              <label class="font-medium">Lab section</label>
              <CoreDropdown
                :is-searchable="true"
                v-model="selectedDepartment"
                :items="departments"
              />
            </div>

            <CoreMultiselect
              label="Specific Sex"
              v-model:items-selected="sexSelected"
              :items="GENDER.map((gender) => gender.name)"
              mode="single"
            />

            <div class="w-full flex">
              <CoreActionButton
                :click="
                  () => {
                    addMeasure();
                  }
                "
                color="primary"
                text="Add Measure"
                :icon="addIcon"
              />
            </div>

            <div
              v-if="indicators.length != 0"
              v-for="(indicator, index) in indicators"
              v-bind:key="indicator.id"
              class="border px-5 py-3 rounded"
            >
              <div class="py-3 flex items-center justify-between">
                <h3 class="text-lg text-grameasuresy-700 font-semibold">
                  Measures ({{ index + 1 }})
                </h3>
                <button
                  @click="
                    () => {
                      indicators.splice(index, 1);
                    }
                  "
                >
                  <XMarkIcon class="w-5 h-5" />
                </button>
              </div>
              <div class="grid grid-cols-4 gap-2 mb-3">
                <FormKit
                  type="text"
                  label="Name"
                  validation="required"
                  v-model="indicator.name"
                />
                <div>
                  <p class="font-medium mb-2">Type</p>
                  <CoreDropdown
                    v-model="indicator.type"
                    :items="measureTypes"
                  />
                </div>
                <FormKit type="text" label="Unit" v-model="indicator.unit" />
                <FormKit
                  type="text"
                  label="Description"
                  v-model="indicator.description"
                />
              </div>

              <CoreActionButton
                type="button"
                :click="
                  () => {
                    addRange(index, indicator.type.name);
                  }
                "
                color="primary"
                text="Add New Range"
                :icon="addIcon"
                v-if="!showRange(indicator.type.name.toLowerCase())"
              />

              <div
                class="w-full px-3 py-3"
                v-if="
                  indicator.indicator_ranges.length > 0 &&
                  indicator.type.name === 'Numeric'
                "
                v-for="(range, rangeIndex) in indicator.indicator_ranges"
                :key="range.id"
              >
                <div class="flex items-center space-x-3">
                  <div class="grid grid-cols-2 gap-2">
                    <div class="">
                      <label class="mb-2 text-lg font-semibold text-gray-600"
                        >Age Range</label
                      >
                      <div class="grid grid-cols-2 gap-2">
                        <FormKit
                          type="number"
                          label="Minimum"
                          validation="required"
                          v-model="range.min_age"
                        />
                        <FormKit
                          type="number"
                          label="Maximum"
                          validation="required"
                          v-model="range.max_age"
                        />
                        <div class="flex flex-col">
                          <label class="font-medium">Sex</label>
                          <CoreDropdown
                            v-model="range.gender"
                            :items="GENDER"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="">
                      <label class="mb-2 text-lg font-semibold text-gray-600"
                        >Measure Range</label
                      >
                      <div class="grid grid-cols-2 gap-2">
                        <FormKit
                          type="number"
                          number
                          step="any"
                          label="Minimum"
                          validation="required|required"
                          v-model="range.upper_range"
                        />
                        <FormKit
                          type="number"
                          number
                          step="any"
                          label="Maximum"
                          validation="required|required"
                          v-model="range.lower_range"
                        />
                      </div>
                    </div>
                    <FormKit
                      type="text"
                      label="Interpretation"
                      validation="required"
                      v-model="range.interpretation"
                    />
                  </div>
                  <button @click="removeRange(index, rangeIndex)">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div
                class="px-3 py-3"
                v-if="
                  (indicator.indicator_ranges.length > 0 &&
                    indicator.type.name == 'Auto Complete') ||
                  indicator.type.name === 'Alpha Numeric'
                "
                v-for="(autoCompleteItem, k) in indicator.indicator_ranges"
                :key="autoCompleteItem.id"
              >
                <div class="w-full flex items-center justify-center space-x-2">
                  <div class="w-full grid grid-cols-2 gap-4">
                    <FormKit
                      type="text"
                      label="Value"
                      validation="required"
                      v-model="autoCompleteItem.value"
                    />

                    <FormKit
                      type="text"
                      label="Interpretation"
                      validation="required"
                      v-model="autoCompleteItem.interpretation"
                    />
                  </div>

                  <button
                    @click="removeRange(index, k)"
                    class="h-full bg-red-500 flex items-center justify-center rounded text-white px-4 py-2 mt-2"
                  >
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div
                class="flex items-center px-3 py-3 rounded bg-sky-50 mt-3 text-sky-500"
                v-if="showRange(indicator.type.name.toLowerCase())"
              >
                <InformationCircleIcon class="w-5 h-5 mr-2" /> A text box will
                appear for results entry
              </div>
            </div>
          </div>

          <div
            class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
          >
            <CoreOutlinedButton
              type="button"
              :click="
                () => {
                  invalidateForm();
                }
              "
              text="Clear form"
            />
            <CoreActionButton
              type="submit"
              :click="() => {}"
              :loading="loading"
              color="success"
              :icon="saveIcon"
              text="Save Changes"
            />
          </div>
        </FormKit>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  PlusIcon as addIcon,
  XMarkIcon,
  ArrowDownTrayIcon as saveIcon,
  InformationCircleIcon,
} from "@heroicons/vue/24/solid/index.js";
import { reset } from "@formkit/core";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type {
  Autocomplete,
  Department,
  Indicator,
  TestIndicatorType,
  Numeric,
  AlphaNumeric,
  Organism,
  Specimen,
  DropdownItem,
  Page,
  Request,
  Response,
  TimeDuration,
} from "@/types";

import TestCatalogModule from "@/repository/modules/test-catalog";
import { TIME_DURATION } from "@/utils/constants";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

const router = useRouter();
const cookie = useCookie("token");
const testCatalogModule = new TestCatalogModule();

const loadingSpecimens = ref<boolean>(false);
const indicators = ref<Indicator[]>([]);
const name = ref<string>("");
const shortName = ref<string>("");
const turnAroundTime = ref<string>("");
const measureTypes = ref<TestIndicatorType[]>([]);
const loading = ref<boolean>(false);
const specimensSelected = ref<Array<string>>([]);
const rawOrganisms = ref<Array<any>>([]);
const organisms = ref<Array<string>>([]);
const organismsSelected = ref<Array<string>>([]);
const shouldPrintResults = ref<boolean>(false);
const showCultureWorksheet = ref<boolean>(false);
const durations = ref<TimeDuration[]>(TIME_DURATION);
const duration = ref<DropdownItem>({ name: SELECT_VALUE_TYPE });
const rawSpecimens = ref([]);
const specimens = ref<Array<string>>([]);
const departments = ref<Department[]>([]);
const sexSelected = ref<string>("");
const selectedDepartment = ref<Department>({ name: SELECT_VALUE_TYPE });
const pages: Page = TEST_TYPE_PAGE_META;

/**
 * Asynchronously loads organisms from the test catalog module.
 *
 * This function retrieves organisms using the getOrganisms method of the testCatalogModule.
 * It expects a cookie value to be passed as an argument to the getOrganisms method.
 * If the data is successfully retrieved, it updates the rawOrganisms and organisms reactive variables.
 * In case of an error, it logs the error to the console.
 *
 * @returns {Promise<void>} A promise that resolves to void.
 */
const loadOrganisms = async (): Promise<void> => {
  const { data, error }: Response = await testCatalogModule.getOrganisms(
    `${cookie.value}`
  );
  if (data.value) {
    rawOrganisms.value = data.value;
    organisms.value = data.value.map((organism: Organism) => organism.name);
  }
  if (error.value) {
    console.error("could not load organisms: ", error.value);
  }
};

/**
 * Loads specimens from the test catalog module.
 * @async
 * @returns {Promise<void>} A Promise that resolves when specimens are loaded.
 */
const loadSpecimens = async (): Promise<void> => {
  const { data, error }: Response = await testCatalogModule.getSpecimens(
    `${cookie.value}`
  );
  if (data.value) {
    rawSpecimens.value = data.value;
    specimens.value = data.value.map((specimen: Specimen) => specimen.name);
  }
  if (error.value) {
    loadingSpecimens.value = false;
    console.error("could not load specimens: ", error.value);
  }
};

/**
 * Loads departments from the test catalog module.
 * @async
 * @returns {Promise<void>} A Promise that resolves when departments are loaded.
 */
const loadDepartments = async (): Promise<void> => {
  const { data, error }: Response = await testCatalogModule.getDepartments(
    `${cookie.value}`
  );
  if (data.value) {
    departments.value = data.value;
  }
  if (error.value) {
    console.error("could not load departments: ", error.value);
  }
};

/**
 * Loads test type indicators from the test catalog module.
 * @returns {Promise<void>} A Promise that resolves when test type indicators are loaded.
 */
const loadTestTypeIndicators = async (): Promise<void> => {
  const { data, error }: Response =
    await testCatalogModule.getTestTypesIndicators(`${cookie.value}`);
  if (data.value) {
    measureTypes.value = data.value;
  }
  if (error.value) {
    console.error("could not load test type indicators: ", error.value);
  }
};

/**
 * Adds a range to the indicators based on the selected measure.
 * @param {number} index - The current index of the indicator array.
 * @param {string} indicatorType - The type of the indicator.
 * @returns {void}
 */
async function addRange(index: number, indicatorType: string): Promise<void> {
  try {
    const indicator: Indicator = indicators?.value[index];
    const indicatorRanges = indicator.indicator_ranges;
    let lastItemId: number | undefined = 0;

    if (indicatorRanges.length > 0) {
      const lastItemIndex = indicatorRanges.length - 1;
      lastItemId = indicatorRanges[lastItemIndex].id;
    }

    const newId = lastItemId && lastItemId + 1;
    const defaultRange: any = {
      id: newId,
      interpretation: "",
    };
    const TT_TYPE = indicatorType.toLowerCase();

    if (TT_TYPE.includes("numeric")) {
      defaultRange.age_max = 0;
      defaultRange.age_min = 0;
      defaultRange.upper_range = 0;
      defaultRange.lower_range = 0;
      defaultRange.gender = { name: TEST_TYPES_MESSAGES.SELECT_GENDER };
    } else if (TT_TYPE == "auto complete" || TT_TYPE.includes("alpha")) {
      defaultRange.value = "";
    }
    indicatorRanges.push(defaultRange);
  } catch (error) {
    console.error("Error: ", error);
  }
}

/**
 * Removes a range from the specified indicator's ranges array.
 *
 * @param {number} indicatorIndex - The index of the indicator in the indicators array.
 * @param {number} index - The index of the range to remove from the indicator's ranges array.
 */
const removeRange = (indicatorIndex: number, index: number): void => {
  indicators.value[indicatorIndex].indicator_ranges.splice(index, 1);
};

/**
 * Adds a new measure to the indicators array with default values.
 * @returnvs void
 */
const addMeasure = (): void => {
  indicators.value.push({
    name: "",
    test_indicator_type: 0,
    unit: "",
    type: {
      name: SELECT_MEASURE_TYPE,
    },
    description: "",
    indicator_ranges: [],
    value: "",
    id: String(indicators.value.length + 1),
  });
};

/**
 * @method showRange determines if a given value should show a range.
 * @param {string} value - The value to check against the hidden ranges.
 * @returns {boolean} - Returns true if the value is within the hidden ranges, otherwise false.
 */
const showRange = (value: string): boolean => {
  const hiddenRanges = new Set(["free text", "rich text"]);
  return hiddenRanges.has(value);
};

async function submitForm(): Promise<void> {
  loading.value = true;
  const updatedIndicators = indicators.value.map((indicator: Indicator) => {
    const updatedRanges = indicator.indicator_ranges.map((range: any) => {
      if (range.hasOwnProperty("gender")) {
        const values: Numeric = range;
        return {
          sex: values.gender.name,
          max_age: values.max_age,
          min_age: values.min_age,
          upper_range: values.upper_range,
          lower_range: values.lower_range,
          interpretation: values.interpretation,
        };
      } else {
        delete range["id"];
        return range as Autocomplete | AlphaNumeric;
      }
    });
    return {
      ...indicator,
      indicator_ranges: updatedRanges,
      test_indicator_type: indicator.type.id,
    };
  });

  const request: Request = {
    route: endpoints.testTypes,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      name: name.value,
      short_name: shortName.value,
      sex: sexSelected.value,
      expected_turn_around_time: {
        value: turnAroundTime.value,
        unit: duration.value.name,
      },
      print_device: shouldPrintResults.value,
      department_id: selectedDepartment.value.id,
      specimens: filterArrays(rawSpecimens.value, specimensSelected.value),
      indicators: updatedIndicators,
      organisms: filterArrays(rawOrganisms.value, organismsSelected.value),
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);

  loading.value = pending;

  if (data.value) {
    invalidateForm();
    router.push("/test-catalog/test-types?create=true");
    loading.value = false;
  }

  if (error.value) {
    console.error("error creating test type: ", error.value);
    useNuxtApp().$toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
}

const validateIndicators = (indicators: Indicator[]): Boolean => {
  const genders = new Set(GENDERS_LOWERCASE);
  for (const indicator of indicators) {
    if (indicator.type.name.toLowerCase() === "numeric") {
      for (const range of indicator.indicator_ranges) {
        if (!genders.has(range.gender.name.toLowerCase())) {
          useNuxtApp().$toast.warning(TEST_TYPES_MESSAGES.GENDER_REQUIRED);
          return false;
        }
      }
    }
  }
  return true;
};

const validateDuration = (duration: DropdownItem): Boolean => {
  if (duration.name === SELECT_VALUE_TYPE) {
    useNuxtApp().$toast.warning(TEST_TYPES_MESSAGES.DURATION_REQUIRED);
    return false;
  }
  return true;
};

const validateMeasure = (indicators: Indicator[]): Boolean => {
  for (const indicator of indicators) {
    if (indicator.type.name === SELECT_MEASURE_TYPE) {
      useNuxtApp().$toast.warning(TEST_TYPES_MESSAGES.MEASURE_TYPE_REQUIRED);
      return false;
    }
  }
  return true;
};

/**
 * @method validator validates non-formkit inputs
 * @returns {boolean}
 */
const validator = (): Boolean => {
  const isValidIndicators: Boolean = validateIndicators(indicators.value);
  const isValidDuration: Boolean = validateDuration(duration.value);
  const isValidMeasure: Boolean = validateMeasure(indicators.value);
  return isValidIndicators && isValidDuration && isValidMeasure;
};

watch(
  () => indicators.value.map((indicator: Indicator) => indicator.type),
  (__new, __old) => {
    if (__new !== __old) {
      __new.forEach((type: { name: string }, index: number) => {
        if (type !== __old[index]) {
          const measures: Set<String> = new Set(MEASURE_TYPES);
          if (measures.has(type.name.toLowerCase().split(" ").join(""))) {
            const indicatorType: string = indicators.value[index].type.name;
            addRange(index, indicatorType);
          }
        }
      });
    }
  },
  { deep: true }
);

const invalidateForm = (): void => {
  reset("submitForm");
};

loadSpecimens();
loadDepartments();
loadTestTypeIndicators();
loadOrganisms();
addMeasure();
</script>
