<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="mt-5 flex items-center space-x-3">
            <div>
                <img src="@/assets/images/user.png" class="w-16 h-16 mr-2 object-cover rounded-full" alt="user-icon"/>
            </div>
            <div class="space-y-0.5">
                <h3 class="text-lg font-semibold uppercase">
                    {{ authStore.user.username }}
                </h3>
                <p class="font-normal">
                    {{ `${authStore.user.first_name} ${authStore.user.last_name}` }}
                </p>

            </div>
        </div>

        <div class="py-5">
            <div class="w-full flex items-center border-b">
                <button v-for="(tab, index) in tabs" :key="index" @click="activeTab = index"
                    :class="activeTab == index ? 'flex items-center px-1.5 py-2 text-white border-b-2 border-sky-600 bg-sky-500 font-medium' : 'px-2 py-2 hover:bg-sky-200 font-medium hover:text-sky-500 transition duration-150 flex items-center'">
                    <IdentificationIcon v-if="index == 0" class="w-5 h-5 mr-2"/>
                    <PencilSquareIcon v-if="index == 1" class="w-5 h-5 mr-2"/>
                    {{ tab }}
                </button>
            </div>
            <div v-if="activeTab === 0">
                <div class="py-3 space-y-2.5">
                    <div class="grid grid-cols-3 gap-2">
                        <div class="w-full flex flex-col space-y-1">
                            <label class="font-semibold text-lg">Username</label>
                            <p class="underline">{{ authStore.user.username }}</p>
                        </div>
                        <div class="w-full flex flex-col space-y-1">
                            <label class="font-semibold text-lg">First name</label>
                            <p class="underline">{{ authStore.user.first_name }}</p>
                        </div>
                        <div class="w-full flex flex-col space-y-1">
                            <label class="font-semibold text-lg">Middle name</label>
                            <p class="underline">{{ authStore.user.middle_name }}</p>
                        </div>
                        <div class="w-full flex flex-col space-y-1">
                            <label class="font-semibold text-lg">Last name</label>
                            <p class="underline">{{ authStore.user.last_name }}</p>
                        </div>
                        <div class="w-full flex flex-col space-y-1">
                            <label class="font-semibold text-lg">Date Of Birth</label>
                            <p class="underline">{{ authStore.user.date_of_birth }}</p>
                        </div>
                    </div>
                    <div class="w-full flex flex-col space-y-1">
                        <label class="font-semibold text-lg">Roles</label>
                        <div class="flex flex-wrap gap-2">
                            <div v-for="(role, index) in authStore.user.roles" :key="index"
                                class="border rounded px-3 py-1.5">
                                {{ role.role_name }}
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex flex-col space-y-1">
                        <label class="font-semibold text-lg">Departments</label>
                        <div class="flex flex-wrap gap-2">
                            <div v-for="(department, index) in authStore.user.departments" :key="index"
                                class="border rounded px-3 py-1.5">
                                {{ department.name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="activeTab === 1">
                <div class="border-b pb-2">
                    <div class="py-3">
                        <h3 class="text-xl font-semibold">Username</h3>
                        <p>Please enter your desired username below and save changes. </p>
                    </div>
                    <FormKit type="form" submit-label="Update" @submit="changeUsername" :actions="false"
                        #default="{ value }">
                        <FormKit type="text" name="username" label="Username" validation="required" v-model="username" />
                        <div class="flex items-center justify-end space-x-2 mt-5">
                            <CoreActionButton type="submit" :icon="saveIcon" text="Save changes" color="success"
                                :click="(() => { })" :loading="updating" />
                        </div>
                    </FormKit>
                </div>
                <div class="py-3">
                    <h3 class="text-xl font-semibold">Password</h3>
                    <p>Please enter your current password to change your password</p>
                </div>
                <FormKit type="form" submit-label="Update" @submit="changePassword" :actions="false" #default="{ value }">
                    <div class="py-3 space-y-2.5">
                        <FormKit type="password" label="Old password" validation="required" v-model="oldPassword" />
                        <FormKit type="group">
                            <FormKit type="password" name="password" label="Password" validation="required"
                                v-model="newPassword" validation-visibility="live" />
                            <FormKit type="password" label="Confirm new password" name="password_confirm"
                                validation="required|confirm" validation-label="Password confirmation"
                                validation-visibility="live" />
                        </FormKit>
                    </div>
                    <div class="flex items-center justify-end space-x-2 mt-5">
                        <CoreActionButton type="submit" :icon="saveIcon" text="Save changes" color="success"
                            :click="(() => { })" :loading="loading" />
                    </div>
                </FormKit>
            </div>
        </div>

    </div>
</template>

<script lang="ts">

import { ArrowDownTrayIcon, IdentificationIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js';
import fetchRequest from '@/services/fetch';
import { endpoints } from '@/services/endpoints';
import { useAuthStore } from '@/store/auth';
import type { Page, Request, Response } from '@/types';
import Package from '@/package.json';

export default {
    name: 'settings',
    setup() {
        definePageMeta({
            layout: 'dashboard',
            middleware: ['auth']
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Settings`
        });
    },
    data() {
        return {
            saveIcon: ArrowDownTrayIcon,
            pages: [
                {
                    name: 'Home',
                    link: '/home'
                }
            ] as Page,
            tabs: [
                "Personal Information",
                "Credentials",
            ] as Array<string>,
            activeTab: 0 as number,
            loading: false as boolean,
            updating: false as boolean,
            cookie: useCookie('token'),
            oldPassword: '' as string,
            newPassword: '' as string,
            username: '' as string,
            authStore: useAuthStore()
        };
    },
    methods: {
        async changePassword(): Promise<void> {
            this.loading = true;
            const request: Request = {
                route: `${endpoints.users}/change_password/${this.authStore.user.id}`,
                method: 'PUT',
                token: `${this.cookie}`,
                body: {
                    "user": {
                        "old_password": this.oldPassword,
                        "password": this.newPassword
                    }
                }
            };
            const { data, error, pending }: Response = await fetchRequest(request);
            this.loading = pending;
            if (data.value) {
                const { logUserOut } = useAuthStore();
                const { lastKnownRoute } = useRouteStore();

                // Capture current route before logout
                if (this.$route.path && this.$route.path !== '/' && this.$route.path !== '/login') {
                    lastKnownRoute(this.$route.path);
                }

                this.loading = false;
                useNuxtApp().$toast.success(`Password changed successfully!`);
                logUserOut();
                this.$router.push('/');
            }
            if (error.value) {
                this.loading = false;
                useNuxtApp().$toast.error(`${error.value.data.error}`);
                console.error(error.value);
            }
        },
        async changeUsername(): Promise<void> {
            this.updating = true;
            const request: Request = {
                route: `${endpoints.users}/change_username/${this.authStore.user.id}`,
                method: 'PUT',
                token: `${this.cookie}`,
                body: {
                    "user": {
                        "username": this.username,
                    }
                }
            };
            const { data, error, pending }: Response = await fetchRequest(request);
            this.updating = pending;
            if (data.value) {
                const { logUserOut } = useAuthStore();
                const { lastKnownRoute } = useRouteStore();

                // Capture current route before logout
                if (this.$route.path && this.$route.path !== '/' && this.$route.path !== '/login') {
                    lastKnownRoute(this.$route.path);
                }

                this.updating = false;
                useNuxtApp().$toast.success(`Username changed successfully!`);
                logUserOut();
                this.$router.push('/');
            }
            if (error.value) {
                this.updating = false;
                useNuxtApp().$toast.error(`${error.value.data.error}`);
                console.error(error.value);
            }
        }
    },
    components: { IdentificationIcon, PencilSquareIcon }
};
</script>
