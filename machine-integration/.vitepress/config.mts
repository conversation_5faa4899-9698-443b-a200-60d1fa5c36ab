import { with<PERSON><PERSON><PERSON><PERSON> } from "vitepress-plugin-mermaid";

export default withMermaid({
  title: "Machine Integration Manual",
  description: "Comprehensive guide for integrating Laboratory Machines with LIS (Laboratory Information System) systems",
  themeConfig: {
    nav: [
      { text: 'Home', link: '/' },
      { text: 'Guide', link: '/guide/introduction' }
    ],
    sidebar: [
      {
        text: 'Guide',
        items: [
          { text: 'Introduction', link: '/guide/introduction' },
          { text: 'Driver Overview', link: '/guide/installation'},
          { text: 'Machine Protocols', link: '/guide/protocols' },
          { text: 'Communication Setup', link: '/guide/setup' },
          { text: 'Data Processing', link: '/guide/data-processing' },
          { text: 'Transferring Data', link: '/guide/data-transfer' },
          { text: 'LIS Data Handling', link: '/guide/data-handling' },
          { text: 'Available Drivers', items: [
            { text: 'BC20', link:'/guide/drivers/bc20'},
            { text: 'BC2800', link:'/guide/drivers/bc2800'},
            { text: '<PERSON><PERSON><PERSON>', link: '/guide/drivers/genex<PERSON>'},
            { text: 'DxH 560', link: '/guide/drivers/dhx560'},
            {
              text: 'XP 300', link: '/guide/drivers/xp300'
            },{ text: 'XN 1000', link: '/guide/drivers/xn1000'}
            ,{ text: 'BS430', link: '/guide/drivers/bs430'},
            { text: 'XL640', link: '/guide/drivers/xl640'}
          ] },
          { text: 'Troubleshooting', link: '/guide/troubleshooting' }
        ]
      }
    ],
    search: {
      provider: 'local'
    },
    socialLinks: [
      { icon: 'github', link: 'https://github.com/EGPAFMalawiHIS/lis-machine-integration-manual.git' }
    ]
  },
  mermaid:{
    //mermaidConfig !theme here works for ligth mode since dark theme is forced in dark mode
  },
})
