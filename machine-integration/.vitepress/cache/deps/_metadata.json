{"hash": "71726770", "configHash": "92d8d615", "lockfileHash": "d31826fb", "browserHash": "b96c5086", "optimized": {"vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "c8183e2a", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../../node_modules/@vue/devtools-api/lib/esm/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "2aba591a", "needsInterop": false}, "@braintree/sanitize-url": {"src": "../../../../node_modules/@braintree/sanitize-url/dist/index.js", "file": "@braintree_sanitize-url.js", "fileHash": "45e8e930", "needsInterop": true}, "dayjs": {"src": "../../../../node_modules/dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "797b4044", "needsInterop": true}, "debug": {"src": "../../../../node_modules/debug/src/browser.js", "file": "debug.js", "fileHash": "542b79cb", "needsInterop": true}, "cytoscape-cose-bilkent": {"src": "../../../../node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js", "file": "cytoscape-cose-bilkent.js", "fileHash": "38f276dc", "needsInterop": true}, "cytoscape": {"src": "../../../../node_modules/cytoscape/dist/cytoscape.cjs.js", "file": "cytoscape.js", "fileHash": "91f605b5", "needsInterop": true}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "044a830a", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../../node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "08dedcb6", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../../node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "e3ac7975", "needsInterop": false}}, "chunks": {"chunk-CKQ4TNQ3": {"file": "chunk-CKQ4TNQ3.js"}, "chunk-Y2F7D3TJ": {"file": "chunk-Y2F7D3TJ.js"}}}